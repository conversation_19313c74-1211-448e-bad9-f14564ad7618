import typesenseClient from './typesense-client';
import { allSchemas, EntityType, schemaMap } from './collection-schemas';

// Initialize collections
export const initializeCollections = async () => {
  try {
    // Get existing collections with a longer timeout
    const existingCollections = await typesenseClient.collections().retrieve();
    const existingCollectionNames = Array.isArray(existingCollections)
      ? existingCollections.map(c => c.name)
      : [];

    // Create collections that don't exist
    for (const schema of allSchemas) {
      if (!existingCollectionNames.includes(schema.name)) {
        try {
          await typesenseClient.collections().create(schema);
          console.log(`Created collection: ${schema.name}`);
        } catch (error) {
          // Just log the error but continue with other collections
          console.warn(`Error creating collection ${schema.name}, will use local fallback:`, error);
        }
      } else {
        console.log(`Collection ${schema.name} already exists`);
      }
    }
    return true;
  } catch (error) {
    // Log the error but don't fail the entire application
    if (import.meta.env.DEV) {
      console.warn('Error initializing collections, will use local fallback:', error);
    }

    // Create a mock local storage for search data if Typesense is unavailable
    try {
      if (typeof window !== 'undefined') {
        // Initialize local storage for search if it doesn't exist
        if (!localStorage.getItem('local_search_data')) {
          localStorage.setItem('local_search_data', JSON.stringify({
            jobs: [],
            candidates: [],
            clients: [],
            settings: []
          }));
        }
      }
    } catch (localStorageError) {
      if (import.meta.env.DEV) {
        console.error('Error initializing local search fallback:', localStorageError);
      }
    }

    return false;
  }
};

// Index a document
export const indexDocument = async <T extends Record<string, any>>(
  collectionName: EntityType,
  document: T
) => {
  try {
    // Add entity_type field if not present
    const documentWithType = {
      ...document,
      entity_type: collectionName,
    };

    // Index the document
    return await typesenseClient
      .collections(collectionName)
      .documents()
      .upsert(documentWithType);
  } catch (error) {
    console.error(`Error indexing document in ${collectionName}:`, error);
    throw error;
  }
};

// Index multiple documents
export const indexDocuments = async <T extends Record<string, any>>(
  collectionName: EntityType,
  documents: T[]
) => {
  try {
    // Add entity_type field to all documents
    const documentsWithType = documents.map(doc => ({
      ...doc,
      entity_type: collectionName,
    }));

    // Index the documents
    return await typesenseClient
      .collections(collectionName)
      .documents()
      .import(documentsWithType, { action: 'upsert' });
  } catch (error) {
    console.error(`Error batch indexing documents in ${collectionName}:`, error);
    throw error;
  }
};

// Delete a document
export const deleteDocument = async (
  collectionName: EntityType,
  documentId: string
) => {
  try {
    return await typesenseClient
      .collections(collectionName)
      .documents(documentId)
      .delete();
  } catch (error) {
    console.error(`Error deleting document ${documentId} from ${collectionName}:`, error);
    throw error;
  }
};

// Delete all documents in a collection
export const clearCollection = async (collectionName: EntityType) => {
  try {
    return await typesenseClient.collections(collectionName).documents().delete();
  } catch (error) {
    console.error(`Error clearing collection ${collectionName}:`, error);
    throw error;
  }
};
