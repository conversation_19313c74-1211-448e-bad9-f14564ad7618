import { EntityType } from './collection-schemas';

// Define search result types
export interface SearchResultItem {
  id: string;
  title: string;
  description?: string;
  entity_type: EntityType;
  [key: string]: any;
}

export interface GroupedSearchResults {
  [key: string]: {
    count: number;
    hits: SearchResultItem[];
  };
}

export interface SearchResults {
  found: number;
  grouped_hits?: GroupedSearchResults;
  hits?: SearchResultItem[];
  facet_counts?: any[];
  page: number;
  request_params?: any;
  search_time_ms?: number;
}

// Cache for search results
const searchCache = new Map<string, { timestamp: number; results: SearchResults }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Get API base URL
const getApiUrl = () => {
  return import.meta.env.VITE_API_URL || 'http://localhost:3001/api';
};

// Search across all collections
export const searchAll = async (
  query: string,
  options: {
    page?: number;
    perPage?: number;
    filterBy?: string;
    sortBy?: string;
    groupBy?: string;
    useCache?: boolean;
  } = {}
): Promise<SearchResults> => {
  const {
    page = 1,
    perPage = 10,
    filterBy,
    sortBy,
    groupBy = 'entity_type',
    useCache = true,
  } = options;

  // Create a cache key
  const cacheKey = JSON.stringify({ query, page, perPage, filterBy, sortBy, groupBy });

  // Check cache if enabled
  if (useCache) {
    const cached = searchCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached.results;
    }
  }

  try {
    // Build query parameters
    const params = new URLSearchParams({
      q: query,
      page: page.toString(),
      per_page: perPage.toString(),
      group_by: groupBy,
      group_limit: '3',
    });

    if (filterBy) params.append('filter_by', filterBy);
    if (sortBy) params.append('sort_by', sortBy);

    // Call backend API
    const response = await fetch(`${getApiUrl()}/search?${params.toString()}`);

    if (!response.ok) {
      throw new Error(`Search API error: ${response.status}`);
    }

    const formattedResults: SearchResults = await response.json();

    // Store in cache
    if (useCache) {
      searchCache.set(cacheKey, {
        timestamp: Date.now(),
        results: formattedResults,
      });
    }

    return formattedResults;
  } catch (error) {
    console.error('Error searching:', error);
    // Return empty results instead of throwing
    return {
      found: 0,
      hits: [],
      page,
      search_time_ms: 0,
      request_params: { q: query, page, per_page: perPage },
    };
  }
};

// Clear the search cache
export const clearSearchCache = () => {
  searchCache.clear();
};

// Search within a specific collection
export const searchCollection = async (
  collectionName: EntityType,
  query: string,
  options: {
    page?: number;
    perPage?: number;
    filterBy?: string;
    sortBy?: string;
    useCache?: boolean;
  } = {}
): Promise<SearchResults> => {
  const {
    page = 1,
    perPage = 10,
    filterBy,
    sortBy,
    useCache = true,
  } = options;

  // Create a cache key
  const cacheKey = JSON.stringify({ collection: collectionName, query, page, perPage, filterBy, sortBy });

  // Check cache if enabled
  if (useCache) {
    const cached = searchCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return cached.results;
    }
  }

  try {
    // Build query parameters
    const params = new URLSearchParams({
      q: query,
      page: page.toString(),
      per_page: perPage.toString(),
    });

    if (filterBy) params.append('filter_by', filterBy);
    if (sortBy) params.append('sort_by', sortBy);

    // Call backend API for specific collection
    const response = await fetch(`${getApiUrl()}/search/${collectionName}?${params.toString()}`);

    if (!response.ok) {
      throw new Error(`Search API error: ${response.status}`);
    }

    const formattedResults: SearchResults = await response.json();

    // Store in cache
    if (useCache) {
      searchCache.set(cacheKey, {
        timestamp: Date.now(),
        results: formattedResults,
      });
    }

    return formattedResults;
  } catch (error) {
    console.error(`Error searching in ${collectionName}:`, error);
    // Return empty results instead of throwing
    return {
      found: 0,
      hits: [],
      page,
      search_time_ms: 0,
      request_params: { q: query, page, per_page: perPage },
    };
  }
};
