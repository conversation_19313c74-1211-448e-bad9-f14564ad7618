import { CollectionCreateSchema } from 'typesense/lib/Typesense/Collections';

// Base schema for all searchable entities
const baseSchema: Partial<CollectionCreateSchema> = {
  default_sorting_field: 'created_at',
  fields: [
    { name: 'id', type: 'string' },
    { name: 'title', type: 'string' },
    { name: 'description', type: 'string', optional: true },
    { name: 'created_at', type: 'int64' },
    { name: 'updated_at', type: 'int64' },
    { name: 'entity_type', type: 'string', facet: true },
  ],
};

// Job collection schema
export const jobsSchema: CollectionCreateSchema = {
  name: 'jobs',
  ...baseSchema,
  fields: [
    ...(baseSchema.fields || []),
    { name: 'company', type: 'string', facet: true, optional: true },
    { name: 'location', type: 'string', facet: true, optional: true },
    { name: 'salary_range', type: 'string', facet: true, optional: true },
    { name: 'job_type', type: 'string', facet: true, optional: true },
    { name: 'status', type: 'string', facet: true },
    { name: 'skills', type: 'string[]', facet: true, optional: true },
  ],
};

// Candidate collection schema
export const candidatesSchema: CollectionCreateSchema = {
  name: 'candidates',
  ...baseSchema,
  fields: [
    ...(baseSchema.fields || []),
    { name: 'name', type: 'string' },
    { name: 'email', type: 'string' },
    { name: 'phone', type: 'string', optional: true },
    { name: 'location', type: 'string', facet: true, optional: true },
    { name: 'skills', type: 'string[]', facet: true, optional: true },
    { name: 'experience_years', type: 'int32', facet: true, optional: true },
    { name: 'status', type: 'string', facet: true },
    { name: 'secondary_status', type: 'string', facet: true, optional: true },
  ],
};

// Client collection schema
export const clientsSchema: CollectionCreateSchema = {
  name: 'clients',
  ...baseSchema,
  fields: [
    ...(baseSchema.fields || []),
    { name: 'company_name', type: 'string' },
    { name: 'contact_name', type: 'string' },
    { name: 'email', type: 'string' },
    { name: 'phone', type: 'string', optional: true },
    { name: 'industry', type: 'string', facet: true, optional: true },
    { name: 'location', type: 'string', facet: true, optional: true },
  ],
};

// Settings collection schema
export const settingsSchema: CollectionCreateSchema = {
  name: 'settings',
  ...baseSchema,
  fields: [
    ...(baseSchema.fields || []),
    { name: 'section', type: 'string', facet: true }, // Main section (profile, notifications, etc.)
    { name: 'category', type: 'string', facet: true, optional: true }, // Sub-category within section
    { name: 'setting_type', type: 'string', facet: true }, // Type: page, option, toggle, etc.
    { name: 'route', type: 'string' }, // Navigation route/path
    { name: 'keywords', type: 'string[]', facet: true, optional: true }, // Additional searchable keywords
  ],
};

// Events collection schema
export const eventsSchema: CollectionCreateSchema = {
  name: 'events',
  ...baseSchema,
  fields: [
    ...(baseSchema.fields || []),
    { name: 'event_type', type: 'string', facet: true }, // meeting, interview, deadline, etc.
    { name: 'start_date', type: 'int64' }, // Event start timestamp
    { name: 'end_date', type: 'int64', optional: true }, // Event end timestamp
    { name: 'location', type: 'string', optional: true }, // Event location
    { name: 'participants', type: 'string[]', facet: true, optional: true }, // Event participants
    { name: 'status', type: 'string', facet: true }, // scheduled, completed, cancelled
    { name: 'priority', type: 'string', facet: true, optional: true }, // high, medium, low
  ],
};

// Messages collection schema
export const messagesSchema: CollectionCreateSchema = {
  name: 'messages',
  ...baseSchema,
  fields: [
    ...(baseSchema.fields || []),
    { name: 'sender', type: 'string' }, // Message sender
    { name: 'recipient', type: 'string', optional: true }, // Message recipient
    { name: 'message_type', type: 'string', facet: true }, // internal, email, sms, etc.
    { name: 'status', type: 'string', facet: true }, // sent, delivered, read, draft
    { name: 'thread_id', type: 'string', optional: true }, // For message threading
    { name: 'priority', type: 'string', facet: true, optional: true }, // high, medium, low
    { name: 'tags', type: 'string[]', facet: true, optional: true }, // Message tags/categories
  ],
};

// Notifications collection schema
export const notificationsSchema: CollectionCreateSchema = {
  name: 'notifications',
  ...baseSchema,
  fields: [
    ...(baseSchema.fields || []),
    { name: 'notification_type', type: 'string', facet: true }, // system, reminder, alert, etc.
    { name: 'priority', type: 'string', facet: true }, // high, medium, low
    { name: 'status', type: 'string', facet: true }, // unread, read, dismissed
    { name: 'recipient', type: 'string' }, // Notification recipient
    { name: 'action_url', type: 'string', optional: true }, // URL for notification action
    { name: 'category', type: 'string', facet: true, optional: true }, // job, candidate, system, etc.
    { name: 'expires_at', type: 'int64', optional: true }, // Notification expiration
  ],
};

// All schemas
export const allSchemas = [
  jobsSchema,
  candidatesSchema,
  clientsSchema,
  settingsSchema,
  eventsSchema,
  messagesSchema,
  notificationsSchema
];

// Schema map for easy access
export const schemaMap = {
  jobs: jobsSchema,
  candidates: candidatesSchema,
  clients: clientsSchema,
  settings: settingsSchema,
  events: eventsSchema,
  messages: messagesSchema,
  notifications: notificationsSchema,
};

export type EntityType = keyof typeof schemaMap;
