import { EntityType } from './collection-schemas';

// Initialize search - simplified for backend API approach
export const initializeSearch = async () => {
  try {
    if (import.meta.env.DEV) {
      console.log('Initializing search...');
    }

    // Test if the search API is available
    try {
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';
      const response = await fetch(`${apiUrl}/search?q=test&page=1&per_page=1`);

      if (response.ok) {
        if (import.meta.env.DEV) {
          console.log('Search API is available and working');
        }
        return true;
      } else {
        if (import.meta.env.DEV) {
          console.warn('Search API is not responding correctly, but search will still be available');
        }
        return true; // Don't block the app if search API is not available
      }
    } catch (apiError) {
      if (import.meta.env.DEV) {
        console.warn('Search API is not available, but search will still be available:', apiError);
      }
      return true; // Don't block the app if search API is not available
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Error initializing search:', error);
      console.warn('Search will operate in limited mode');
    }
    // Don't show error toast to users, just log it
    return true; // Return true anyway to not block the application
  }
};

// Function to index a single entity via API
export const indexEntity = async <T extends Record<string, any>>(
  entityType: EntityType,
  _entity: T // Prefixed with underscore to indicate intentionally unused
) => {
  try {
    // In the new backend API approach, indexing is handled automatically
    // when entities are created/updated through the API endpoints
    if (import.meta.env.DEV) {
      console.log(`Entity indexing for ${entityType} is handled by the backend API`);
    }
    return true;
  } catch (error) {
    console.error(`Error indexing ${entityType}:`, error);
    return false;
  }
};
