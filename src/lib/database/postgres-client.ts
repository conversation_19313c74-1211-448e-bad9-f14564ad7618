import { Pool } from 'pg';

// Get environment variables
const host = import.meta.env.VITE_POSTGRES_HOST || 'localhost';
const port = parseInt(import.meta.env.VITE_POSTGRES_PORT || '5432');
const database = import.meta.env.VITE_POSTGRES_DATABASE || 'postgres';
const user = import.meta.env.VITE_POSTGRES_USER || 'postgres';
const password = import.meta.env.VITE_POSTGRES_PASSWORD || 'postgres';

// Create a connection pool
const pool = new Pool({
  host,
  port,
  database,
  user,
  password,
  ssl: process.env.NODE_ENV === 'production',
});

// Log initialization for debugging in development only
if (import.meta.env.DEV) {
  console.log('PostgreSQL client initialized with host:', host);
}

// Helper function to execute queries
export async function query(text: string, params?: any[]) {
  const start = Date.now();
  try {
    const res = await pool.query(text, params);
    const duration = Date.now() - start;

    // Log queries in development only
    if (import.meta.env.DEV) {
      console.log('Executed query', { text, duration, rows: res.rowCount });
    }

    return res;
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error('Error executing query', { text, error });
    }
    throw error;
  }
}

// Export the pool for direct use if needed
export default pool;
