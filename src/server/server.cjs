/**
 * Express Server for the API (JavaScript version)
 */

const express = require('express');
const cors = require('cors');
const db = require('./utils/db-connection.cjs');
const QueryBuilder = require('./utils/queryBuilder.cjs');
require('dotenv').config();

// Create Express application
const app = express();
const PORT = process.env.VITE_SERVER_PORT || 3001;

// Express server initializing

// Initialize database connection
db.initializeConnection()
  .then(connected => {
    // Database connection handled silently
  })
  .catch(error => {
    // Database connection errors handled silently
  });

// QueryBuilder is now imported from ./utils/queryBuilder.cjs

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Bulk operations endpoint
app.post('/api/bulk/delete/candidates', async (req, res) => {
  console.log('Received bulk delete request for candidates');
  console.log('Request body:', req.body);

  try {
    const { ids } = req.body;
    console.log('IDs to delete:', ids);

    // Validate request
    if (!Array.isArray(ids) || ids.length === 0) {
      console.log('Invalid request: Expected an array of candidate IDs');
      return res.status(400).json({
        success: false,
        error: 'Invalid request. Expected an array of candidate IDs.'
      });
    }

    // Use transaction to ensure atomicity
    let deletedCount = 0;
    await withTransaction(async (client) => {
      // First delete related records (job applications, etc.)
      try {
        const jobApplicationsResult = await client.query(
          'DELETE FROM job_applications WHERE candidate_id = ANY($1) RETURNING id',
          [ids]
        );
        console.log(`Deleted ${jobApplicationsResult.rowCount} job applications`);
      } catch (error) {
        console.log('Job applications table might not exist, continuing with deletion');
      }

      // Delete communications if the table exists
      try {
        const communicationsResult = await client.query(
          'DELETE FROM communications WHERE candidate_id = ANY($1)',
          [ids]
        );
        console.log(`Deleted ${communicationsResult.rowCount} communications`);
      } catch (error) {
        console.log('Communications table might not exist, continuing with deletion');
      }

      // Then delete the candidates
      const result = await client.query(
        'DELETE FROM candidates WHERE id = ANY($1) RETURNING id',
        [ids]
      );

      deletedCount = result.rowCount;
      console.log(`Deleted ${deletedCount} candidates`);
    });

    const response = {
      success: true,
      deletedCount,
      message: `Successfully deleted ${deletedCount} candidate(s).`
    };
    console.log('Bulk delete successful:', response);
    res.json(response);
  } catch (error) {
    console.error('Error performing bulk delete operation:', error);
    const errorResponse = {
      success: false,
      error: 'Database error during bulk delete operation.'
    };
    console.log('Sending error response:', errorResponse);
    res.status(500).json(errorResponse);
  }
});

// Use the query and transaction methods from our database connection utility
const { query, withTransaction } = db;

// Enhanced health check endpoint - available at both /health and /api/health
const healthCheckHandler = async (req, res) => {
  try {
    // Check database connection
    const isConnected = await db.checkConnection();
    const connectionStatus = db.getConnectionStatus();

    res.json({
      status: isConnected ? 'ok' : 'degraded',
      database: 'PostgreSQL',
      version: process.env.npm_package_version,
      connection: {
        isConnected,
        lastChecked: connectionStatus.lastChecked,
        poolStatus: connectionStatus.poolStatus,
        retryCount: connectionStatus.retryCount
      },
      environment: process.env.NODE_ENV || 'development'
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      status: 'error',
      database: 'PostgreSQL',
      error: error.message,
      version: process.env.npm_package_version
    });
  }
};

// Register the health check endpoint at both paths
app.get('/health', healthCheckHandler);
app.get('/api/health', healthCheckHandler);

// Candidates endpoints
app.get('/api/candidates', async (req, res) => {
  try {
    const { limit, offset, sort, status } = req.query;

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('candidates');

    // Add filters if provided
    if (status) {
      queryBuilder.where('status', '=', status);
    }

    // Add sorting
    if (sort === 'name_asc') {
      queryBuilder.orderBy('first_name', 'ASC');
    } else if (sort === 'name_desc') {
      queryBuilder.orderBy('first_name', 'DESC');
    } else {
      queryBuilder.orderBy('created_at', 'DESC');
    }

    // Add pagination
    if (limit) {
      queryBuilder.limit(parseInt(limit));
    }

    if (offset) {
      queryBuilder.offset(parseInt(offset));
    }

    // Execute query
    const { text, params } = queryBuilder.buildSelect();
    const result = await query(text, params);

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching candidates:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Get candidate by ID
app.get('/api/candidates/:id', async (req, res) => {
  try {
    const result = await query(
      'SELECT * FROM candidates WHERE id = $1',
      [req.params.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching candidate:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create a new candidate
app.post('/api/candidates', async (req, res) => {
  try {
    const {
      // Basic Information
      first_name,
      last_name,
      email,
      phone,
      location,

      // Professional Information
      resume_url,
      linkedin_url,
      github_url,
      portfolio_url,
      twitter_url,
      skills,
      experience_years,
      education,
      current_company,
      current_position,
      desired_salary,
      salary_currency,
      availability_date,
      source,

      // Status Information
      status,
      secondary_status,
      english_level,

      // Assessment & Interview Information
      interview_score,
      interview_notes,
      challenge,
      challenge_notes,
      challenge_feedback,
      drive_score,
      resilience_score,
      collaboration_score,
      result: candidateResult,

      // Process Information
      notes,
      stargety_id,
      is_duplicate,

      // Incubator Status
      is_in_incubator
    } = req.body;

    // Validate required fields
    if (!first_name || !last_name || !email) {
      return res.status(400).json({ error: 'First name, last name, and email are required' });
    }

    // Helper function to convert empty strings to null for numeric fields
    const parseNumeric = (value) => {
      if (value === '' || value === null || value === undefined || value === 'Not specified') {
        return null;
      }
      const parsed = parseFloat(value);
      return isNaN(parsed) ? null : parsed;
    };

    // Helper function to convert empty strings to null for integer fields
    const parseInteger = (value) => {
      if (value === '' || value === null || value === undefined || value === 'Not specified') {
        return null;
      }
      const parsed = Number.parseInt(value);
      return isNaN(parsed) ? null : parsed;
    };

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('candidates');
    const { text, params } = queryBuilder.buildInsert({
      // Basic Information
      first_name,
      last_name,
      email,
      phone,
      location,

      // Professional Information
      resume_url,
      linkedin_url,
      github_url,
      portfolio_url,
      twitter_url,
      skills: skills || [],
      experience_years: parseInteger(experience_years),
      education,
      current_company,
      current_position,
      desired_salary: parseNumeric(desired_salary),
      salary_currency: salary_currency || 'USD',
      availability_date: availability_date ? new Date(availability_date) : null,
      source,

      // Status Information
      status: status || 'new',
      secondary_status,
      english_level,

      // Assessment & Interview Information
      interview_score: parseNumeric(interview_score),
      interview_notes,
      challenge,
      challenge_notes,
      challenge_feedback,
      drive_score: parseNumeric(drive_score),
      resilience_score: parseNumeric(resilience_score),
      collaboration_score: parseNumeric(collaboration_score),
      result: candidateResult,

      // Process Information
      notes,
      stargety_id,
      is_duplicate: is_duplicate || 'new',

      // Incubator Status
      is_in_incubator: is_in_incubator || false,

      // Timestamps
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating candidate:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Update an existing candidate
app.put('/api/candidates/:id', async (req, res) => {
  console.log(`Updating candidate with ID: ${req.params.id}`);
  try {
    const {
      // Basic Information
      first_name,
      last_name,
      email,
      phone,
      location,

      // Professional Information
      experience_years,
      education,
      current_company,
      current_position,
      desired_salary,
      salary_currency,
      availability_date,
      source,
      skills,

      // Status Information
      status,
      secondary_status,
      english_level,

      // URLs and Links
      portfolio_url,
      resume_url,
      linkedin_url,
      github_url,
      twitter_url,

      // Interview & Assessment
      interview_score,
      interview_notes,
      challenge,
      challenge_notes,
      challenge_feedback,

      // Scoring
      drive_score,
      resilience_score,
      collaboration_score,

      // Additional Fields
      notes,
      stargety_id,
      is_duplicate,
      result: candidateResult,

      // Incubator Status
      is_in_incubator
    } = req.body;

    console.log('Received update data:', JSON.stringify(req.body, null, 2));

    // Helper function to convert empty strings to null for numeric fields
    const parseNumeric = (value) => {
      if (value === '' || value === null || value === undefined || value === 'Not specified') {
        return null;
      }
      const parsed = parseFloat(value);
      return isNaN(parsed) ? null : parsed;
    };

    // Helper function to convert empty strings to null for integer fields
    const parseInteger = (value) => {
      if (value === '' || value === null || value === undefined || value === 'Not specified') {
        return null;
      }
      const parsed = Number.parseInt(value);
      return isNaN(parsed) ? null : parsed;
    };

    // Helper function to convert empty strings to null for string fields
    const emptyToNull = (value) => {
      return (value === '' || value === 'Not specified') ? null : value;
    };

    // Check if candidate exists
    const existingResult = await query(
      'SELECT id FROM candidates WHERE id = $1',
      [req.params.id]
    );

    if (existingResult.rows.length === 0) {
      console.log(`Candidate with ID ${req.params.id} not found`);
      return res.status(404).json({ error: 'Candidate not found' });
    }

    // Check if email is unique (excluding current candidate)
    if (email) {
      const emailCheckResult = await query(
        'SELECT id FROM candidates WHERE email = $1 AND id != $2',
        [email, req.params.id]
      );

      if (emailCheckResult.rows.length > 0) {
        console.log(`Email ${email} already exists for another candidate`);
        return res.status(409).json({ error: 'Email already exists' });
      }
    }

    // Update candidate
    const queryBuilder = new QueryBuilder('candidates');
    queryBuilder.where('id', '=', req.params.id);

    const updateData = {
      updated_at: new Date().toISOString()
    };

    // Only include fields that are provided - include all new fields
    // Basic Information
    if (first_name !== undefined) updateData.first_name = first_name;
    if (last_name !== undefined) updateData.last_name = last_name;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;
    if (location !== undefined) updateData.location = location;

    // Professional Information
    if (experience_years !== undefined) updateData.experience_years = parseInteger(experience_years);
    if (education !== undefined) updateData.education = education;
    if (current_company !== undefined) updateData.current_company = current_company;
    if (current_position !== undefined) updateData.current_position = current_position;
    if (desired_salary !== undefined) updateData.desired_salary = parseNumeric(desired_salary);
    if (salary_currency !== undefined) updateData.salary_currency = salary_currency;
    if (availability_date !== undefined) {
      // Handle date validation - reject invalid dates like "Not specified"
      if (availability_date && availability_date !== 'Not specified' && availability_date !== '') {
        const parsedDate = new Date(availability_date);
        updateData.availability_date = isNaN(parsedDate.getTime()) ? null : parsedDate;
      } else {
        updateData.availability_date = null;
      }
    }
    if (source !== undefined) updateData.source = source;
    if (skills !== undefined) updateData.skills = Array.isArray(skills) ? skills : [];

    // Status Information
    if (status !== undefined) updateData.status = status;
    if (secondary_status !== undefined) updateData.secondary_status = emptyToNull(secondary_status);
    if (english_level !== undefined) updateData.english_level = emptyToNull(english_level);

    // URLs and Links
    if (portfolio_url !== undefined) updateData.portfolio_url = portfolio_url;
    if (resume_url !== undefined) updateData.resume_url = resume_url;
    if (linkedin_url !== undefined) updateData.linkedin_url = linkedin_url;
    if (github_url !== undefined) updateData.github_url = github_url;
    if (twitter_url !== undefined) updateData.twitter_url = twitter_url;

    // Interview & Assessment
    if (interview_score !== undefined) updateData.interview_score = parseNumeric(interview_score);
    if (interview_notes !== undefined) updateData.interview_notes = interview_notes;
    if (challenge !== undefined) updateData.challenge = challenge;
    if (challenge_notes !== undefined) updateData.challenge_notes = challenge_notes;
    if (challenge_feedback !== undefined) updateData.challenge_feedback = challenge_feedback;

    // Scoring
    if (drive_score !== undefined) updateData.drive_score = parseNumeric(drive_score);
    if (resilience_score !== undefined) updateData.resilience_score = parseNumeric(resilience_score);
    if (collaboration_score !== undefined) updateData.collaboration_score = parseNumeric(collaboration_score);

    // Additional Fields
    if (notes !== undefined) updateData.notes = notes;
    if (stargety_id !== undefined) updateData.stargety_id = emptyToNull(stargety_id);
    if (is_duplicate !== undefined) updateData.is_duplicate = is_duplicate;
    if (candidateResult !== undefined) updateData.result = candidateResult;

    // Incubator Status
    if (is_in_incubator !== undefined) updateData.is_in_incubator = is_in_incubator;

    console.log('Prepared update data:', JSON.stringify(updateData, null, 2));

    // Use QueryBuilder for consistent SQL generation
    const { text, params } = queryBuilder.buildUpdate(updateData);
    console.log('QueryBuilder SQL Query:', text);
    console.log('QueryBuilder SQL Parameters:', JSON.stringify(params, null, 2));

    const updateResult = await query(text, params);
    console.log('Update successful, returned data:', JSON.stringify(updateResult.rows[0], null, 2));

    res.json(updateResult.rows[0]);
  } catch (error) {
    console.error('Error updating candidate:', error);
    console.error('Error stack:', error.stack);
    console.error('Request body:', JSON.stringify(req.body, null, 2));
    console.error('Candidate ID:', req.params.id);

    // Provide more specific error messages
    if (error.code === '23505') {
      return res.status(409).json({ error: 'Duplicate entry', message: 'A candidate with this email already exists' });
    } else if (error.code === '23502') {
      return res.status(400).json({ error: 'Missing required field', message: error.message });
    } else if (error.code === '22P02') {
      return res.status(400).json({ error: 'Invalid data type', message: 'One or more fields have invalid data types' });
    } else if (error.code === '23514') {
      return res.status(400).json({ error: 'Invalid field value', message: 'One or more fields contain invalid values. Please check secondary_status and other constrained fields.' });
    }

    res.status(500).json({ error: 'Database error', message: error.message, code: error.code });
  }
});

// Jobs endpoints
app.get('/api/jobs', async (req, res) => {
  try {
    const { limit, offset, sort, status, client_id } = req.query;

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('jobs');

    // Add filters if provided
    if (status) {
      queryBuilder.where('status', '=', status);
    }

    if (client_id) {
      queryBuilder.where('client_id', '=', client_id);
    }

    // Add sorting
    if (sort === 'title_asc') {
      queryBuilder.orderBy('title', 'ASC');
    } else if (sort === 'title_desc') {
      queryBuilder.orderBy('title', 'DESC');
    } else if (sort === 'priority_high') {
      queryBuilder.orderBy('priority', 'ASC'); // Assuming 'high' is first alphabetically
    } else {
      queryBuilder.orderBy('created_at', 'DESC');
    }

    // Add pagination
    if (limit) {
      queryBuilder.limit(parseInt(limit));
    }

    if (offset) {
      queryBuilder.offset(parseInt(offset));
    }

    // Execute query
    const { text, params } = queryBuilder.buildSelect();
    const result = await query(text, params);

    // Get client information for each job
    const jobsWithClients = await Promise.all(
      result.rows.map(async (job) => {
        if (job.client_id) {
          const clientResult = await query(
            'SELECT id, company_name FROM clients WHERE id = $1',
            [job.client_id]
          );

          return {
            ...job,
            client: clientResult.rows[0] || null
          };
        }
        return job;
      })
    );

    res.json(jobsWithClients);
  } catch (error) {
    console.error('Error fetching jobs:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Get job by ID
app.get('/api/jobs/:id', async (req, res) => {
  try {
    const result = await query(
      'SELECT * FROM jobs WHERE id = $1',
      [req.params.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Job not found' });
    }

    // Get client information
    const job = result.rows[0];
    if (job.client_id) {
      const clientResult = await query(
        'SELECT id, company_name FROM clients WHERE id = $1',
        [job.client_id]
      );

      job.client = clientResult.rows[0] || null;
    }

    res.json(job);
  } catch (error) {
    console.error('Error fetching job:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create a new job
app.post('/api/jobs', async (req, res) => {
  try {
    const {
      client_id,
      title,
      description,
      requirements,
      location,
      salary_min,
      salary_max,
      salary_currency,
      employment_type,
      remote_type,
      status,
      priority,
      created_by
    } = req.body;

    // Validate required fields
    if (!title) {
      return res.status(400).json({ error: 'Job title is required' });
    }

    if (!client_id) {
      return res.status(400).json({ error: 'Client selection is required' });
    }

    // Check if client exists
    const clientResult = await db.query(
      'SELECT id FROM clients WHERE id = $1',
      [client_id]
    );

    if (clientResult.rows.length === 0) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('jobs');
    const { text, params } = queryBuilder.buildInsert({
      client_id,
      title,
      description,
      requirements,
      location,
      salary_min,
      salary_max,
      salary_currency: salary_currency || 'USD',
      employment_type,
      remote_type,
      status: status || 'open',
      priority: priority || 'medium',
      created_by,
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating job:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Clients endpoints
app.get('/api/clients', async (req, res) => {
  try {
    const { limit, offset, sort, status } = req.query;

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('clients');

    // Add filters if provided
    if (status) {
      queryBuilder.where('status', '=', status);
    }

    // Add sorting
    if (sort === 'name_asc') {
      queryBuilder.orderBy('company_name', 'ASC');
    } else if (sort === 'name_desc') {
      queryBuilder.orderBy('company_name', 'DESC');
    } else {
      queryBuilder.orderBy('created_at', 'DESC');
    }

    // Add pagination
    if (limit) {
      queryBuilder.limit(parseInt(limit));
    }

    if (offset) {
      queryBuilder.offset(parseInt(offset));
    }

    // Execute query
    const { text, params } = queryBuilder.buildSelect();
    const result = await query(text, params);

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching clients:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Get client by ID
app.get('/api/clients/:id', async (req, res) => {
  try {
    const result = await query(
      'SELECT * FROM clients WHERE id = $1',
      [req.params.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Client not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching client:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create a new client
app.post('/api/clients', async (req, res) => {
  try {
    const { company_name, industry, website, logo_url, address, city, state, zip_code, country, phone, email, status, notes } = req.body;

    // Validate required fields
    if (!company_name) {
      return res.status(400).json({ error: 'Company name is required' });
    }

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('clients');
    const { text, params } = queryBuilder.buildInsert({
      company_name,
      industry,
      website,
      logo_url,
      address,
      city,
      state,
      zip_code,
      country,
      phone,
      email,
      status: status || 'active',
      notes,
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating client:', error.message);
    res.status(500).json({ error: 'Database error' });
  }
});

// Update client
app.put('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { company_name, industry, website, logo_url, address, city, state, zip_code, country, phone, email, status, notes } = req.body;

    // Check if client exists
    const existingResult = await query(
      'SELECT id FROM clients WHERE id = $1',
      [id]
    );

    if (existingResult.rows.length === 0) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // Build update query using QueryBuilder
    const queryBuilder = new QueryBuilder('clients');
    queryBuilder.where('id', '=', id);

    const updateData = {
      updated_at: new Date()
    };

    // Only include fields that are provided
    const fields = [
      'company_name', 'industry', 'website', 'logo_url', 'address', 'city',
      'state', 'zip_code', 'country', 'phone', 'email', 'status', 'notes'
    ];

    fields.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    const { text, params } = queryBuilder.buildUpdate(updateData);
    const result = await query(text, params);

    // Return the updated client
    const updatedClient = await query(
      'SELECT * FROM clients WHERE id = $1',
      [id]
    );

    res.json(updatedClient.rows[0]);
  } catch (error) {
    console.error('Error updating client:', error.message);
    res.status(500).json({ error: 'Database error' });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err.message);
  res.status(500).json({
    success: false,
    message: err.message || 'Internal server error',
  });
});

// Client Notes endpoints
app.get('/api/client-notes/client/:clientId', async (req, res) => {
  try {
    const { clientId } = req.params;

    // Query to get notes with user information
    const queryText = `
      SELECT
        cn.id,
        cn.client_id,
        cn.user_id,
        cn.content,
        cn.created_at,
        cn.updated_at,
        u.first_name || ' ' || COALESCE(u.last_name, '') as user_name,
        u.email as user_email,
        u.avatar_url as user_avatar
      FROM client_notes cn
      LEFT JOIN users u ON cn.user_id = u.id
      WHERE cn.client_id = $1
      ORDER BY cn.created_at DESC
    `;

    const result = await query(queryText, [clientId]);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching client notes:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create a new note for a client
app.post('/api/client-notes', async (req, res) => {
  try {
    const { client_id, content, user_id } = req.body;

    // Validate required fields
    if (!client_id || !content || !user_id) {
      return res.status(400).json({
        error: 'Missing required fields: client_id, content, and user_id are required'
      });
    }

    // Verify client exists
    const clientCheck = await query(
      'SELECT id FROM clients WHERE id = $1',
      [client_id]
    );

    if (clientCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // Verify user exists
    const userCheck = await query(
      'SELECT id FROM users WHERE id = $1',
      [user_id]
    );

    if (userCheck.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Create the note
    const queryBuilder = new QueryBuilder('client_notes');
    const { text, params } = queryBuilder.buildInsert({
      client_id,
      user_id,
      content,
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);

    // Return the created note with user information
    const noteWithUser = await query(`
      SELECT
        cn.id,
        cn.client_id,
        cn.user_id,
        cn.content,
        cn.created_at,
        cn.updated_at,
        u.first_name || ' ' || COALESCE(u.last_name, '') as user_name,
        u.email as user_email,
        u.avatar_url as user_avatar
      FROM client_notes cn
      LEFT JOIN users u ON cn.user_id = u.id
      WHERE cn.id = $1
    `, [result.rows[0].id]);

    res.status(201).json(noteWithUser.rows[0]);
  } catch (error) {
    console.error('Error creating client note:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Candidate Notes endpoints
app.get('/api/candidate-notes/candidate/:candidateId', async (req, res) => {
  try {
    const { candidateId } = req.params;

    // Query to get notes with user information
    const queryText = `
      SELECT
        cn.id,
        cn.candidate_id,
        cn.user_id,
        cn.content,
        cn.created_at,
        cn.updated_at,
        u.first_name || ' ' || COALESCE(u.last_name, '') as user_name,
        u.email as user_email,
        u.avatar_url as user_avatar
      FROM candidate_notes cn
      LEFT JOIN users u ON cn.user_id = u.id
      WHERE cn.candidate_id = $1
      ORDER BY cn.created_at DESC
    `;

    const result = await query(queryText, [candidateId]);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching candidate notes:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create a new note for a candidate
app.post('/api/candidate-notes', async (req, res) => {
  try {
    const { candidate_id, content, user_id } = req.body;

    // Validate required fields
    if (!candidate_id || !content || !user_id) {
      return res.status(400).json({
        error: 'Missing required fields: candidate_id, content, and user_id are required'
      });
    }

    // Verify candidate exists
    const candidateCheck = await query(
      'SELECT id FROM candidates WHERE id = $1',
      [candidate_id]
    );

    if (candidateCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    // Create the note
    const queryBuilder = new QueryBuilder('candidate_notes');
    const { text, params } = queryBuilder.buildInsert({
      candidate_id,
      user_id,
      content,
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);

    // Return the created note with user information
    const noteWithUser = await query(`
      SELECT
        cn.id,
        cn.candidate_id,
        cn.user_id,
        cn.content,
        cn.created_at,
        cn.updated_at,
        u.first_name || ' ' || COALESCE(u.last_name, '') as user_name,
        u.email as user_email,
        u.avatar_url as user_avatar
      FROM candidate_notes cn
      LEFT JOIN users u ON cn.user_id = u.id
      WHERE cn.id = $1
    `, [result.rows[0].id]);

    res.status(201).json(noteWithUser.rows[0]);
  } catch (error) {
    console.error('Error creating candidate note:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Update a client note
app.put('/api/client-notes/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { content, user_id } = req.body;

    // Validate required fields
    if (!content || !user_id) {
      return res.status(400).json({
        error: 'Missing required fields: content and user_id are required'
      });
    }

    // Verify note exists and user owns it
    const noteCheck = await query(
      'SELECT id, user_id FROM client_notes WHERE id = $1',
      [id]
    );

    if (noteCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Note not found' });
    }

    if (noteCheck.rows[0].user_id !== user_id) {
      return res.status(403).json({ error: 'Not authorized to update this note' });
    }

    // Update the note
    const queryBuilder = new QueryBuilder('client_notes');
    queryBuilder.where('id', '=', id);

    const { text, params } = queryBuilder.buildUpdate({
      content,
      updated_at: new Date()
    });

    await query(text, params);

    // Return the updated note with user information
    const updatedNote = await query(`
      SELECT
        cn.id,
        cn.client_id,
        cn.user_id,
        cn.content,
        cn.created_at,
        cn.updated_at,
        u.first_name || ' ' || COALESCE(u.last_name, '') as user_name,
        u.email as user_email,
        u.avatar_url as user_avatar
      FROM client_notes cn
      LEFT JOIN users u ON cn.user_id = u.id
      WHERE cn.id = $1
    `, [id]);

    res.json(updatedNote.rows[0]);
  } catch (error) {
    console.error('Error updating client note:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Delete a client note
app.delete('/api/client-notes/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id } = req.body;

    // Validate required fields
    if (!user_id) {
      return res.status(400).json({
        error: 'Missing required field: user_id is required'
      });
    }

    // Verify note exists and user owns it
    const noteCheck = await query(
      'SELECT id, user_id FROM client_notes WHERE id = $1',
      [id]
    );

    if (noteCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Note not found' });
    }

    if (noteCheck.rows[0].user_id !== user_id) {
      return res.status(403).json({ error: 'Not authorized to delete this note' });
    }

    // Delete the note
    await query('DELETE FROM client_notes WHERE id = $1', [id]);

    res.status(204).send();
  } catch (error) {
    console.error('Error deleting client note:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Typesense client setup
const Typesense = require('typesense');

const typesenseClient = new Typesense.Client({
  nodes: [
    {
      host: process.env.VITE_TYPESENSE_HOST || 'typesense',
      port: parseInt(process.env.VITE_TYPESENSE_PORT || '8108'),
      protocol: process.env.VITE_TYPESENSE_PROTOCOL || 'http',
    },
  ],
  apiKey: process.env.VITE_TYPESENSE_API_KEY || 'xyz123',
  connectionTimeoutSeconds: 5,
  retryIntervalSeconds: 1,
  numRetries: 3,
});

// Search endpoints
app.get('/api/search', async (req, res) => {
  try {
    const {
      q: query,
      page = 1,
      per_page: perPage = 10,
      filter_by: filterBy,
      sort_by: sortBy = '_text_match:desc',
      group_by: groupBy = 'entity_type',
      group_limit: groupLimit = 3
    } = req.query;

    if (!query || query.trim() === '') {
      return res.json({
        found: 0,
        hits: [],
        page: parseInt(page),
        search_time_ms: 0,
      });
    }

    const searchParameters = {
      q: query,
      query_by: 'title,description,name,company_name,contact_name,skills',
      filter_by: filterBy || '',
      sort_by: sortBy,
      page: parseInt(page),
      per_page: parseInt(perPage),
      group_by: groupBy,
      group_limit: parseInt(groupLimit),
    };

    // Perform multi-collection search - search each collection separately
    const searchResults = await typesenseClient.multiSearch.perform({
      searches: [
        {
          collection: 'jobs',
          ...searchParameters,
        },
        {
          collection: 'candidates',
          ...searchParameters,
        },
        {
          collection: 'clients',
          ...searchParameters,
        },
      ],
    });

    const results = searchResults?.results?.[0] || { found: 0, hits: [], search_time_ms: 0 };
    const formattedResults = {
      found: results.found || 0,
      page: parseInt(page),
      search_time_ms: results.search_time_ms || 0,
      request_params: searchParameters,
    };

    // Format grouped results if grouping is enabled
    if (groupBy && results.grouped_hits && Array.isArray(results.grouped_hits)) {
      const groupedHits = {};

      results.grouped_hits.forEach((group) => {
        if (group && group.group_key && Array.isArray(group.group_key) && group.group_key.length > 0) {
          const groupName = group.group_key[0];
          const hits = Array.isArray(group.hits)
            ? group.hits
                .filter(hit => hit && hit.document)
                .map((hit) => hit.document)
            : [];

          groupedHits[groupName] = {
            count: group.found || hits.length,
            hits,
          };
        }
      });

      formattedResults.grouped_hits = groupedHits;
    } else if (results.hits && Array.isArray(results.hits)) {
      formattedResults.hits = results.hits
        .filter(hit => hit && hit.document)
        .map((hit) => hit.document);
    }

    res.json(formattedResults);
  } catch (error) {
    console.error('Search error:', error);
    res.json({
      found: 0,
      hits: [],
      page: parseInt(req.query.page || 1),
      search_time_ms: 0,
      error: 'Search temporarily unavailable',
    });
  }
});

// Search specific collection
app.get('/api/search/:collection', async (req, res) => {
  try {
    const { collection } = req.params;
    const {
      q: query,
      page = 1,
      per_page: perPage = 10,
      filter_by: filterBy,
      sort_by: sortBy = '_text_match:desc'
    } = req.query;

    if (!query || query.trim() === '') {
      return res.json({
        found: 0,
        hits: [],
        page: parseInt(page),
        search_time_ms: 0,
      });
    }

    const searchParameters = {
      q: query,
      query_by: 'title,description,name,company_name,contact_name,skills',
      filter_by: filterBy || '',
      sort_by: sortBy,
      page: parseInt(page),
      per_page: parseInt(perPage),
    };

    const results = await typesenseClient
      .collections(collection)
      .documents()
      .search(searchParameters);

    const formattedResults = {
      found: results?.found || 0,
      page: parseInt(page),
      search_time_ms: results?.search_time_ms || 0,
      request_params: searchParameters,
    };

    if (results?.hits && Array.isArray(results.hits)) {
      formattedResults.hits = results.hits
        .filter(hit => hit && hit.document)
        .map((hit) => hit.document);
    } else {
      formattedResults.hits = [];
    }

    res.json(formattedResults);
  } catch (error) {
    console.error(`Search error in ${req.params.collection}:`, error);
    res.json({
      found: 0,
      hits: [],
      page: parseInt(req.query.page || 1),
      search_time_ms: 0,
      error: 'Search temporarily unavailable',
    });
  }
});

// Data synchronization endpoints
app.post('/api/search/sync/:collection', async (req, res) => {
  try {
    const { collection } = req.params;
    let syncCount = 0;

    switch (collection) {
      case 'jobs':
        const jobsResult = await query(`
          SELECT
            j.id,
            j.title,
            COALESCE(j.description, '') as description,
            COALESCE(j.location, '') as location,
            CASE
              WHEN j.salary_min IS NOT NULL AND j.salary_max IS NOT NULL
              THEN CONCAT(j.salary_min, ' - ', j.salary_max, ' ', j.salary_currency)
              ELSE ''
            END as salary_range,
            COALESCE(j.employment_type, '') as job_type,
            j.status,
            EXTRACT(EPOCH FROM j.created_at) * 1000 as created_at,
            EXTRACT(EPOCH FROM j.updated_at) * 1000 as updated_at,
            COALESCE(c.company_name, '') as company
          FROM jobs j
          LEFT JOIN clients c ON c.id = j.client_id
        `);

        const jobDocuments = jobsResult.rows.map(job => ({
          id: job.id,
          title: job.title || '',
          description: job.description || '',
          entity_type: 'jobs',
          created_at: parseInt(job.created_at) || Date.now(),
          updated_at: parseInt(job.updated_at) || Date.now(),
          company: job.company || '',
          location: job.location || '',
          salary_range: job.salary_range || '',
          job_type: job.job_type || '',
          status: job.status || 'open',
          skills: [], // Add skills if available in your schema
        }));

        if (jobDocuments.length > 0) {
          await typesenseClient.collections('jobs').documents().import(jobDocuments, { action: 'upsert' });
          syncCount = jobDocuments.length;
        }
        break;

      case 'candidates':
        const candidatesResult = await query(`
          SELECT
            id,
            CONCAT(first_name, ' ', COALESCE(last_name, '')) as name,
            CONCAT(first_name, ' ', COALESCE(last_name, '')) as title,
            COALESCE(email, '') as email,
            COALESCE(phone, '') as phone,
            COALESCE(location, '') as location,
            skills,
            experience_years,
            status,
            secondary_status,
            EXTRACT(EPOCH FROM created_at) * 1000 as created_at,
            EXTRACT(EPOCH FROM updated_at) * 1000 as updated_at,
            CONCAT('Candidate with ', COALESCE(experience_years, 0), ' years of experience') as description
          FROM candidates
        `);

        const candidateDocuments = candidatesResult.rows.map(candidate => ({
          id: candidate.id,
          title: candidate.title || '',
          description: candidate.description || '',
          entity_type: 'candidates',
          created_at: parseInt(candidate.created_at) || Date.now(),
          updated_at: parseInt(candidate.updated_at) || Date.now(),
          name: candidate.name || '',
          email: candidate.email || '',
          phone: candidate.phone || '',
          location: candidate.location || '',
          skills: Array.isArray(candidate.skills) ? candidate.skills : [],
          experience_years: candidate.experience_years || 0,
          status: candidate.status || 'new',
          secondary_status: candidate.secondary_status || '',
        }));

        if (candidateDocuments.length > 0) {
          await typesenseClient.collections('candidates').documents().import(candidateDocuments, { action: 'upsert' });
          syncCount = candidateDocuments.length;
        }
        break;

      case 'clients':
        const clientsResult = await query(`
          SELECT
            id,
            company_name,
            company_name as title,
            COALESCE(email, '') as email,
            COALESCE(phone, '') as phone,
            COALESCE(industry, '') as industry,
            CONCAT(COALESCE(city, ''),
                   CASE WHEN city IS NOT NULL AND state IS NOT NULL THEN ', ' ELSE '' END,
                   COALESCE(state, ''),
                   CASE WHEN (city IS NOT NULL OR state IS NOT NULL) AND country IS NOT NULL THEN ', ' ELSE '' END,
                   COALESCE(country, '')) as location,
            EXTRACT(EPOCH FROM created_at) * 1000 as created_at,
            EXTRACT(EPOCH FROM updated_at) * 1000 as updated_at,
            CONCAT('Company in ', COALESCE(industry, 'various'), ' industry') as description
          FROM clients
        `);

        const clientDocuments = clientsResult.rows.map(client => ({
          id: client.id,
          title: client.title || '',
          description: client.description || '',
          entity_type: 'clients',
          created_at: parseInt(client.created_at) || Date.now(),
          updated_at: parseInt(client.updated_at) || Date.now(),
          company_name: client.company_name || '',
          contact_name: 'Contact Person', // Default value since we don't have this field
          email: client.email || '',
          phone: client.phone || '',
          industry: client.industry || '',
          location: client.location || '',
        }));

        if (clientDocuments.length > 0) {
          await typesenseClient.collections('clients').documents().import(clientDocuments, { action: 'upsert' });
          syncCount = clientDocuments.length;
        }
        break;

      default:
        return res.status(400).json({ error: 'Invalid collection name' });
    }

    res.json({
      success: true,
      collection,
      syncCount,
      message: `Successfully synced ${syncCount} ${collection} to search index`,
    });
  } catch (error) {
    console.error(`Error syncing ${req.params.collection}:`, error);
    res.status(500).json({
      success: false,
      error: 'Failed to sync data to search index',
      message: error.message,
    });
  }
});

// Sync all collections
app.post('/api/search/sync', async (req, res) => {
  try {
    const collections = ['jobs', 'candidates', 'clients'];
    const results = {};

    for (const collection of collections) {
      try {
        const syncResponse = await fetch(`http://localhost:${PORT}/api/search/sync/${collection}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
        });
        const syncResult = await syncResponse.json();
        results[collection] = syncResult;
      } catch (error) {
        results[collection] = { success: false, error: error.message };
      }
    }

    res.json({
      success: true,
      message: 'Sync completed for all collections',
      results,
    });
  } catch (error) {
    console.error('Error syncing all collections:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to sync all collections',
      message: error.message,
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;
