/**
 * Express Server for the API (JavaScript version)
 */

const express = require('express');
const cors = require('cors');
const db = require('./utils/db-connection.cjs');
const QueryBuilder = require('./utils/queryBuilder.cjs');
require('dotenv').config();

// Create Express application
const app = express();
const PORT = process.env.VITE_SERVER_PORT || 3001;

// Express server initializing

// Initialize database connection
db.initializeConnection()
  .then(connected => {
    // Database connection handled silently
  })
  .catch(error => {
    // Database connection errors handled silently
  });

// QueryBuilder is now imported from ./utils/queryBuilder.cjs

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Bulk operations endpoint
app.post('/api/bulk/delete/candidates', async (req, res) => {
  try {
    const { ids } = req.body;

    // Validate request
    if (!Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid request. Expected an array of candidate IDs.'
      });
    }

    // Use transaction to ensure atomicity
    let deletedCount = 0;
    await withTransaction(async (client) => {
      // First delete related records (job applications, etc.)
      try {
        await client.query(
          'DELETE FROM job_applications WHERE candidate_id = ANY($1)',
          [ids]
        );
      } catch (error) {
        // Job applications table might not exist, continue with deletion
      }

      // Delete communications if the table exists
      try {
        await client.query(
          'DELETE FROM communications WHERE candidate_id = ANY($1)',
          [ids]
        );
      } catch (error) {
        // Communications table might not exist, continue with deletion
      }

      // Then delete the candidates
      const result = await client.query(
        'DELETE FROM candidates WHERE id = ANY($1) RETURNING id',
        [ids]
      );

      deletedCount = result.rowCount;

      // Sync deletions to search index
      try {
        for (const id of ids) {
          await typesenseClient.collections('candidates').documents(id).delete();
        }
      } catch (syncError) {
        console.warn('Failed to sync candidate deletions to search index:', syncError);
        // Don't fail the request if search sync fails
      }
    });

    const response = {
      success: true,
      deletedCount,
      message: `Successfully deleted ${deletedCount} candidate(s).`
    };
    res.json(response);
  } catch (error) {
    console.error('Error performing bulk delete operation:', error);
    res.status(500).json({
      success: false,
      error: 'Database error during bulk delete operation.'
    });
  }
});

// Use the query and transaction methods from our database connection utility
const { query, withTransaction } = db;

// Enhanced health check endpoint - available at both /health and /api/health
const healthCheckHandler = async (req, res) => {
  try {
    // Check database connection
    const isConnected = await db.checkConnection();
    const connectionStatus = db.getConnectionStatus();

    res.json({
      status: isConnected ? 'ok' : 'degraded',
      database: 'PostgreSQL',
      version: process.env.npm_package_version,
      connection: {
        isConnected,
        lastChecked: connectionStatus.lastChecked,
        poolStatus: connectionStatus.poolStatus,
        retryCount: connectionStatus.retryCount
      },
      environment: process.env.NODE_ENV || 'development'
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      status: 'error',
      database: 'PostgreSQL',
      error: error.message,
      version: process.env.npm_package_version
    });
  }
};

// Register the health check endpoint at both paths
app.get('/health', healthCheckHandler);
app.get('/api/health', healthCheckHandler);

// Candidates endpoints
app.get('/api/candidates', async (req, res) => {
  try {
    const { limit, offset, sort, status } = req.query;

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('candidates');

    // Add filters if provided
    if (status) {
      queryBuilder.where('status', '=', status);
    }

    // Add sorting
    if (sort === 'name_asc') {
      queryBuilder.orderBy('first_name', 'ASC');
    } else if (sort === 'name_desc') {
      queryBuilder.orderBy('first_name', 'DESC');
    } else {
      queryBuilder.orderBy('created_at', 'DESC');
    }

    // Add pagination
    if (limit) {
      queryBuilder.limit(parseInt(limit));
    }

    if (offset) {
      queryBuilder.offset(parseInt(offset));
    }

    // Execute query
    const { text, params } = queryBuilder.buildSelect();
    const result = await query(text, params);

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching candidates:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Get candidate by ID
app.get('/api/candidates/:id', async (req, res) => {
  try {
    const result = await query(
      'SELECT * FROM candidates WHERE id = $1',
      [req.params.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching candidate:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create a new candidate
app.post('/api/candidates', async (req, res) => {
  try {
    const {
      // Basic Information
      first_name,
      last_name,
      email,
      phone,
      location,

      // Professional Information
      resume_url,
      linkedin_url,
      github_url,
      portfolio_url,
      twitter_url,
      skills,
      experience_years,
      education,
      current_company,
      current_position,
      desired_salary,
      salary_currency,
      availability_date,
      source,

      // Status Information
      status,
      secondary_status,
      english_level,

      // Assessment & Interview Information
      interview_score,
      interview_notes,
      challenge,
      challenge_notes,
      challenge_feedback,
      drive_score,
      resilience_score,
      collaboration_score,
      result: candidateResult,

      // Process Information
      notes,
      stargety_id,
      is_duplicate,

      // Incubator Status
      is_in_incubator
    } = req.body;

    // Validate required fields
    if (!first_name || !last_name || !email) {
      return res.status(400).json({ error: 'First name, last name, and email are required' });
    }

    // Helper function to convert empty strings to null for numeric fields
    const parseNumeric = (value) => {
      if (value === '' || value === null || value === undefined || value === 'Not specified') {
        return null;
      }
      const parsed = parseFloat(value);
      return isNaN(parsed) ? null : parsed;
    };

    // Helper function to convert empty strings to null for integer fields
    const parseInteger = (value) => {
      if (value === '' || value === null || value === undefined || value === 'Not specified') {
        return null;
      }
      const parsed = Number.parseInt(value);
      return isNaN(parsed) ? null : parsed;
    };

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('candidates');
    const { text, params } = queryBuilder.buildInsert({
      // Basic Information
      first_name,
      last_name,
      email,
      phone,
      location,

      // Professional Information
      resume_url,
      linkedin_url,
      github_url,
      portfolio_url,
      twitter_url,
      skills: skills || [],
      experience_years: parseInteger(experience_years),
      education,
      current_company,
      current_position,
      desired_salary: parseNumeric(desired_salary),
      salary_currency: salary_currency || 'USD',
      availability_date: availability_date ? new Date(availability_date) : null,
      source,

      // Status Information
      status: status || 'new',
      secondary_status,
      english_level,

      // Assessment & Interview Information
      interview_score: parseNumeric(interview_score),
      interview_notes,
      challenge,
      challenge_notes,
      challenge_feedback,
      drive_score: parseNumeric(drive_score),
      resilience_score: parseNumeric(resilience_score),
      collaboration_score: parseNumeric(collaboration_score),
      result: candidateResult,

      // Process Information
      notes,
      stargety_id,
      is_duplicate: is_duplicate || 'new',

      // Incubator Status
      is_in_incubator: is_in_incubator || false,

      // Timestamps
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);
    const newCandidate = result.rows[0];

    // Sync to search index
    try {
      const candidateDocument = {
        id: newCandidate.id,
        title: `${newCandidate.first_name} ${newCandidate.last_name || ''}`.trim(),
        description: `Candidate with ${newCandidate.experience_years || 0} years of experience`,
        entity_type: 'candidates',
        created_at: new Date(newCandidate.created_at).getTime(),
        updated_at: new Date(newCandidate.updated_at).getTime(),
        name: `${newCandidate.first_name} ${newCandidate.last_name || ''}`.trim(),
        email: newCandidate.email || '',
        phone: newCandidate.phone || '',
        location: newCandidate.location || '',
        skills: Array.isArray(newCandidate.skills) ? newCandidate.skills : [],
        experience_years: newCandidate.experience_years || 0,
        status: newCandidate.status || 'new',
        secondary_status: newCandidate.secondary_status || '',
      };

      await typesenseClient.collections('candidates').documents().upsert(candidateDocument);
    } catch (syncError) {
      console.warn('Failed to sync candidate to search index:', syncError);
      // Don't fail the request if search sync fails
    }

    res.status(201).json(newCandidate);
  } catch (error) {
    console.error('Error creating candidate:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Update an existing candidate
app.put('/api/candidates/:id', async (req, res) => {
  try {
    const {
      // Basic Information
      first_name,
      last_name,
      email,
      phone,
      location,

      // Professional Information
      experience_years,
      education,
      current_company,
      current_position,
      desired_salary,
      salary_currency,
      availability_date,
      source,
      skills,

      // Status Information
      status,
      secondary_status,
      english_level,

      // URLs and Links
      portfolio_url,
      resume_url,
      linkedin_url,
      github_url,
      twitter_url,

      // Interview & Assessment
      interview_score,
      interview_notes,
      challenge,
      challenge_notes,
      challenge_feedback,

      // Scoring
      drive_score,
      resilience_score,
      collaboration_score,

      // Additional Fields
      notes,
      stargety_id,
      is_duplicate,
      result: candidateResult,

      // Incubator Status
      is_in_incubator
    } = req.body;

    console.log('Received update data:', JSON.stringify(req.body, null, 2));

    // Helper function to convert empty strings to null for numeric fields
    const parseNumeric = (value) => {
      if (value === '' || value === null || value === undefined || value === 'Not specified') {
        return null;
      }
      const parsed = parseFloat(value);
      return isNaN(parsed) ? null : parsed;
    };

    // Helper function to convert empty strings to null for integer fields
    const parseInteger = (value) => {
      if (value === '' || value === null || value === undefined || value === 'Not specified') {
        return null;
      }
      const parsed = Number.parseInt(value);
      return isNaN(parsed) ? null : parsed;
    };

    // Helper function to convert empty strings to null for string fields
    const emptyToNull = (value) => {
      return (value === '' || value === 'Not specified') ? null : value;
    };

    // Check if candidate exists
    const existingResult = await query(
      'SELECT id FROM candidates WHERE id = $1',
      [req.params.id]
    );

    if (existingResult.rows.length === 0) {
      console.log(`Candidate with ID ${req.params.id} not found`);
      return res.status(404).json({ error: 'Candidate not found' });
    }

    // Check if email is unique (excluding current candidate)
    if (email) {
      const emailCheckResult = await query(
        'SELECT id FROM candidates WHERE email = $1 AND id != $2',
        [email, req.params.id]
      );

      if (emailCheckResult.rows.length > 0) {
        console.log(`Email ${email} already exists for another candidate`);
        return res.status(409).json({ error: 'Email already exists' });
      }
    }

    // Update candidate
    const queryBuilder = new QueryBuilder('candidates');
    queryBuilder.where('id', '=', req.params.id);

    const updateData = {
      updated_at: new Date().toISOString()
    };

    // Only include fields that are provided - include all new fields
    // Basic Information
    if (first_name !== undefined) updateData.first_name = first_name;
    if (last_name !== undefined) updateData.last_name = last_name;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;
    if (location !== undefined) updateData.location = location;

    // Professional Information
    if (experience_years !== undefined) updateData.experience_years = parseInteger(experience_years);
    if (education !== undefined) updateData.education = education;
    if (current_company !== undefined) updateData.current_company = current_company;
    if (current_position !== undefined) updateData.current_position = current_position;
    if (desired_salary !== undefined) updateData.desired_salary = parseNumeric(desired_salary);
    if (salary_currency !== undefined) updateData.salary_currency = salary_currency;
    if (availability_date !== undefined) {
      // Handle date validation - reject invalid dates like "Not specified"
      if (availability_date && availability_date !== 'Not specified' && availability_date !== '') {
        const parsedDate = new Date(availability_date);
        updateData.availability_date = isNaN(parsedDate.getTime()) ? null : parsedDate;
      } else {
        updateData.availability_date = null;
      }
    }
    if (source !== undefined) updateData.source = source;
    if (skills !== undefined) updateData.skills = Array.isArray(skills) ? skills : [];

    // Status Information
    if (status !== undefined) updateData.status = status;
    if (secondary_status !== undefined) updateData.secondary_status = emptyToNull(secondary_status);
    if (english_level !== undefined) updateData.english_level = emptyToNull(english_level);

    // URLs and Links
    if (portfolio_url !== undefined) updateData.portfolio_url = portfolio_url;
    if (resume_url !== undefined) updateData.resume_url = resume_url;
    if (linkedin_url !== undefined) updateData.linkedin_url = linkedin_url;
    if (github_url !== undefined) updateData.github_url = github_url;
    if (twitter_url !== undefined) updateData.twitter_url = twitter_url;

    // Interview & Assessment
    if (interview_score !== undefined) updateData.interview_score = parseNumeric(interview_score);
    if (interview_notes !== undefined) updateData.interview_notes = interview_notes;
    if (challenge !== undefined) updateData.challenge = challenge;
    if (challenge_notes !== undefined) updateData.challenge_notes = challenge_notes;
    if (challenge_feedback !== undefined) updateData.challenge_feedback = challenge_feedback;

    // Scoring
    if (drive_score !== undefined) updateData.drive_score = parseNumeric(drive_score);
    if (resilience_score !== undefined) updateData.resilience_score = parseNumeric(resilience_score);
    if (collaboration_score !== undefined) updateData.collaboration_score = parseNumeric(collaboration_score);

    // Additional Fields
    if (notes !== undefined) updateData.notes = notes;
    if (stargety_id !== undefined) updateData.stargety_id = emptyToNull(stargety_id);
    if (is_duplicate !== undefined) updateData.is_duplicate = is_duplicate;
    if (candidateResult !== undefined) updateData.result = candidateResult;

    // Incubator Status
    if (is_in_incubator !== undefined) updateData.is_in_incubator = is_in_incubator;

    // Use QueryBuilder for consistent SQL generation
    const { text, params } = queryBuilder.buildUpdate(updateData);
    const updateResult = await query(text, params);

    const updatedCandidate = updateResult.rows[0];

    // Sync to search index
    try {
      const candidateDocument = {
        id: updatedCandidate.id,
        title: `${updatedCandidate.first_name} ${updatedCandidate.last_name || ''}`.trim(),
        description: `Candidate with ${updatedCandidate.experience_years || 0} years of experience`,
        entity_type: 'candidates',
        created_at: new Date(updatedCandidate.created_at).getTime(),
        updated_at: new Date(updatedCandidate.updated_at).getTime(),
        name: `${updatedCandidate.first_name} ${updatedCandidate.last_name || ''}`.trim(),
        email: updatedCandidate.email || '',
        phone: updatedCandidate.phone || '',
        location: updatedCandidate.location || '',
        skills: Array.isArray(updatedCandidate.skills) ? updatedCandidate.skills : [],
        experience_years: updatedCandidate.experience_years || 0,
        status: updatedCandidate.status || 'new',
        secondary_status: updatedCandidate.secondary_status || '',
      };

      await typesenseClient.collections('candidates').documents().upsert(candidateDocument);
    } catch (syncError) {
      console.warn('Failed to sync updated candidate to search index:', syncError);
      // Don't fail the request if search sync fails
    }

    res.json(updatedCandidate);
  } catch (error) {
    console.error('Error updating candidate:', error);

    // Provide more specific error messages
    if (error.code === '23505') {
      return res.status(409).json({ error: 'Duplicate entry', message: 'A candidate with this email already exists' });
    } else if (error.code === '23502') {
      return res.status(400).json({ error: 'Missing required field', message: error.message });
    } else if (error.code === '22P02') {
      return res.status(400).json({ error: 'Invalid data type', message: 'One or more fields have invalid data types' });
    } else if (error.code === '23514') {
      return res.status(400).json({ error: 'Invalid field value', message: 'One or more fields contain invalid values. Please check secondary_status and other constrained fields.' });
    }

    res.status(500).json({ error: 'Database error', message: error.message, code: error.code });
  }
});

// Jobs endpoints
app.get('/api/jobs', async (req, res) => {
  try {
    const { limit, offset, sort, status, client_id } = req.query;

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('jobs');

    // Add filters if provided
    if (status) {
      queryBuilder.where('status', '=', status);
    }

    if (client_id) {
      queryBuilder.where('client_id', '=', client_id);
    }

    // Add sorting
    if (sort === 'title_asc') {
      queryBuilder.orderBy('title', 'ASC');
    } else if (sort === 'title_desc') {
      queryBuilder.orderBy('title', 'DESC');
    } else if (sort === 'priority_high') {
      queryBuilder.orderBy('priority', 'ASC'); // Assuming 'high' is first alphabetically
    } else {
      queryBuilder.orderBy('created_at', 'DESC');
    }

    // Add pagination
    if (limit) {
      queryBuilder.limit(parseInt(limit));
    }

    if (offset) {
      queryBuilder.offset(parseInt(offset));
    }

    // Execute query
    const { text, params } = queryBuilder.buildSelect();
    const result = await query(text, params);

    // Get client information for each job
    const jobsWithClients = await Promise.all(
      result.rows.map(async (job) => {
        if (job.client_id) {
          const clientResult = await query(
            'SELECT id, company_name FROM clients WHERE id = $1',
            [job.client_id]
          );

          return {
            ...job,
            client: clientResult.rows[0] || null
          };
        }
        return job;
      })
    );

    res.json(jobsWithClients);
  } catch (error) {
    console.error('Error fetching jobs:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Get job by ID
app.get('/api/jobs/:id', async (req, res) => {
  try {
    const result = await query(
      'SELECT * FROM jobs WHERE id = $1',
      [req.params.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Job not found' });
    }

    // Get client information
    const job = result.rows[0];
    if (job.client_id) {
      const clientResult = await query(
        'SELECT id, company_name FROM clients WHERE id = $1',
        [job.client_id]
      );

      job.client = clientResult.rows[0] || null;
    }

    res.json(job);
  } catch (error) {
    console.error('Error fetching job:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create a new job
app.post('/api/jobs', async (req, res) => {
  try {
    const {
      client_id,
      title,
      description,
      requirements,
      location,
      salary_min,
      salary_max,
      salary_currency,
      employment_type,
      remote_type,
      status,
      priority,
      created_by
    } = req.body;

    // Validate required fields
    if (!title) {
      return res.status(400).json({ error: 'Job title is required' });
    }

    if (!client_id) {
      return res.status(400).json({ error: 'Client selection is required' });
    }

    // Check if client exists
    const clientResult = await db.query(
      'SELECT id FROM clients WHERE id = $1',
      [client_id]
    );

    if (clientResult.rows.length === 0) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('jobs');
    const { text, params } = queryBuilder.buildInsert({
      client_id,
      title,
      description,
      requirements,
      location,
      salary_min,
      salary_max,
      salary_currency: salary_currency || 'USD',
      employment_type,
      remote_type,
      status: status || 'open',
      priority: priority || 'medium',
      created_by,
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);
    const newJob = result.rows[0];

    // Sync to search index
    try {
      // Get company name for the job
      const clientResult = await query('SELECT company_name FROM clients WHERE id = $1', [newJob.client_id]);
      const companyName = clientResult.rows[0]?.company_name || '';

      const jobDocument = {
        id: newJob.id,
        title: newJob.title || '',
        description: newJob.description || '',
        entity_type: 'jobs',
        created_at: new Date(newJob.created_at).getTime(),
        updated_at: new Date(newJob.updated_at).getTime(),
        company: companyName,
        location: newJob.location || '',
        salary_range: newJob.salary_min && newJob.salary_max
          ? `${newJob.salary_min} - ${newJob.salary_max} ${newJob.salary_currency}`
          : '',
        job_type: newJob.employment_type || '',
        status: newJob.status || 'open',
        skills: [],
      };

      await typesenseClient.collections('jobs').documents().upsert(jobDocument);
    } catch (syncError) {
      console.warn('Failed to sync job to search index:', syncError);
      // Don't fail the request if search sync fails
    }

    res.status(201).json(newJob);
  } catch (error) {
    console.error('Error creating job:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Clients endpoints
app.get('/api/clients', async (req, res) => {
  try {
    const { limit, offset, sort, status } = req.query;

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('clients');

    // Add filters if provided
    if (status) {
      queryBuilder.where('status', '=', status);
    }

    // Add sorting
    if (sort === 'name_asc') {
      queryBuilder.orderBy('company_name', 'ASC');
    } else if (sort === 'name_desc') {
      queryBuilder.orderBy('company_name', 'DESC');
    } else {
      queryBuilder.orderBy('created_at', 'DESC');
    }

    // Add pagination
    if (limit) {
      queryBuilder.limit(parseInt(limit));
    }

    if (offset) {
      queryBuilder.offset(parseInt(offset));
    }

    // Execute query
    const { text, params } = queryBuilder.buildSelect();
    const result = await query(text, params);

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching clients:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Get client by ID
app.get('/api/clients/:id', async (req, res) => {
  try {
    const result = await query(
      'SELECT * FROM clients WHERE id = $1',
      [req.params.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Client not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error fetching client:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create a new client
app.post('/api/clients', async (req, res) => {
  try {
    const { company_name, industry, website, logo_url, address, city, state, zip_code, country, phone, email, status, notes } = req.body;

    // Validate required fields
    if (!company_name) {
      return res.status(400).json({ error: 'Company name is required' });
    }

    // Build query using QueryBuilder
    const queryBuilder = new QueryBuilder('clients');
    const { text, params } = queryBuilder.buildInsert({
      company_name,
      industry,
      website,
      logo_url,
      address,
      city,
      state,
      zip_code,
      country,
      phone,
      email,
      status: status || 'active',
      notes,
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);
    const newClient = result.rows[0];

    // Sync to search index
    try {
      const clientDocument = {
        id: newClient.id,
        title: newClient.company_name || '',
        description: `Company in ${newClient.industry || 'various'} industry`,
        entity_type: 'clients',
        created_at: new Date(newClient.created_at).getTime(),
        updated_at: new Date(newClient.updated_at).getTime(),
        company_name: newClient.company_name || '',
        contact_name: 'Contact Person', // Default value
        email: newClient.email || '',
        phone: newClient.phone || '',
        industry: newClient.industry || '',
        location: [newClient.city, newClient.state, newClient.country]
          .filter(Boolean)
          .join(', '),
      };

      await typesenseClient.collections('clients').documents().upsert(clientDocument);
    } catch (syncError) {
      console.warn('Failed to sync client to search index:', syncError);
      // Don't fail the request if search sync fails
    }

    res.status(201).json(newClient);
  } catch (error) {
    console.error('Error creating client:', error.message);
    res.status(500).json({ error: 'Database error' });
  }
});

// Update client
app.put('/api/clients/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { company_name, industry, website, logo_url, address, city, state, zip_code, country, phone, email, status, notes } = req.body;

    // Check if client exists
    const existingResult = await query(
      'SELECT id FROM clients WHERE id = $1',
      [id]
    );

    if (existingResult.rows.length === 0) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // Build update query using QueryBuilder
    const queryBuilder = new QueryBuilder('clients');
    queryBuilder.where('id', '=', id);

    const updateData = {
      updated_at: new Date()
    };

    // Only include fields that are provided
    const fields = [
      'company_name', 'industry', 'website', 'logo_url', 'address', 'city',
      'state', 'zip_code', 'country', 'phone', 'email', 'status', 'notes'
    ];

    fields.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });

    const { text, params } = queryBuilder.buildUpdate(updateData);
    const result = await query(text, params);

    // Return the updated client
    const updatedClient = await query(
      'SELECT * FROM clients WHERE id = $1',
      [id]
    );

    const clientData = updatedClient.rows[0];

    // Sync to search index
    try {
      const clientDocument = {
        id: clientData.id,
        title: clientData.company_name || '',
        description: `Company in ${clientData.industry || 'various'} industry`,
        entity_type: 'clients',
        created_at: new Date(clientData.created_at).getTime(),
        updated_at: new Date(clientData.updated_at).getTime(),
        company_name: clientData.company_name || '',
        contact_name: 'Contact Person', // Default value
        email: clientData.email || '',
        phone: clientData.phone || '',
        industry: clientData.industry || '',
        location: [clientData.city, clientData.state, clientData.country]
          .filter(Boolean)
          .join(', '),
      };

      await typesenseClient.collections('clients').documents().upsert(clientDocument);
    } catch (syncError) {
      console.warn('Failed to sync updated client to search index:', syncError);
      // Don't fail the request if search sync fails
    }

    res.json(clientData);
  } catch (error) {
    console.error('Error updating client:', error.message);
    res.status(500).json({ error: 'Database error' });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err.message);
  res.status(500).json({
    success: false,
    message: err.message || 'Internal server error',
  });
});

// Client Notes endpoints
app.get('/api/client-notes/client/:clientId', async (req, res) => {
  try {
    const { clientId } = req.params;

    // Query to get notes with user information
    const queryText = `
      SELECT
        cn.id,
        cn.client_id,
        cn.user_id,
        cn.content,
        cn.created_at,
        cn.updated_at,
        u.first_name || ' ' || COALESCE(u.last_name, '') as user_name,
        u.email as user_email,
        u.avatar_url as user_avatar
      FROM client_notes cn
      LEFT JOIN users u ON cn.user_id = u.id
      WHERE cn.client_id = $1
      ORDER BY cn.created_at DESC
    `;

    const result = await query(queryText, [clientId]);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching client notes:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create a new note for a client
app.post('/api/client-notes', async (req, res) => {
  try {
    const { client_id, content, user_id } = req.body;

    // Validate required fields
    if (!client_id || !content || !user_id) {
      return res.status(400).json({
        error: 'Missing required fields: client_id, content, and user_id are required'
      });
    }

    // Verify client exists
    const clientCheck = await query(
      'SELECT id FROM clients WHERE id = $1',
      [client_id]
    );

    if (clientCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // Verify user exists
    const userCheck = await query(
      'SELECT id FROM users WHERE id = $1',
      [user_id]
    );

    if (userCheck.rows.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Create the note
    const queryBuilder = new QueryBuilder('client_notes');
    const { text, params } = queryBuilder.buildInsert({
      client_id,
      user_id,
      content,
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);

    // Return the created note with user information
    const noteWithUser = await query(`
      SELECT
        cn.id,
        cn.client_id,
        cn.user_id,
        cn.content,
        cn.created_at,
        cn.updated_at,
        u.first_name || ' ' || COALESCE(u.last_name, '') as user_name,
        u.email as user_email,
        u.avatar_url as user_avatar
      FROM client_notes cn
      LEFT JOIN users u ON cn.user_id = u.id
      WHERE cn.id = $1
    `, [result.rows[0].id]);

    res.status(201).json(noteWithUser.rows[0]);
  } catch (error) {
    console.error('Error creating client note:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Candidate Notes endpoints
app.get('/api/candidate-notes/candidate/:candidateId', async (req, res) => {
  try {
    const { candidateId } = req.params;

    // Query to get notes with user information
    const queryText = `
      SELECT
        cn.id,
        cn.candidate_id,
        cn.user_id,
        cn.content,
        cn.created_at,
        cn.updated_at,
        u.first_name || ' ' || COALESCE(u.last_name, '') as user_name,
        u.email as user_email,
        u.avatar_url as user_avatar
      FROM candidate_notes cn
      LEFT JOIN users u ON cn.user_id = u.id
      WHERE cn.candidate_id = $1
      ORDER BY cn.created_at DESC
    `;

    const result = await query(queryText, [candidateId]);
    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching candidate notes:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Create a new note for a candidate
app.post('/api/candidate-notes', async (req, res) => {
  try {
    const { candidate_id, content, user_id } = req.body;

    // Validate required fields
    if (!candidate_id || !content || !user_id) {
      return res.status(400).json({
        error: 'Missing required fields: candidate_id, content, and user_id are required'
      });
    }

    // Verify candidate exists
    const candidateCheck = await query(
      'SELECT id FROM candidates WHERE id = $1',
      [candidate_id]
    );

    if (candidateCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    // Create the note
    const queryBuilder = new QueryBuilder('candidate_notes');
    const { text, params } = queryBuilder.buildInsert({
      candidate_id,
      user_id,
      content,
      created_at: new Date(),
      updated_at: new Date()
    });

    const result = await query(text, params);

    // Return the created note with user information
    const noteWithUser = await query(`
      SELECT
        cn.id,
        cn.candidate_id,
        cn.user_id,
        cn.content,
        cn.created_at,
        cn.updated_at,
        u.first_name || ' ' || COALESCE(u.last_name, '') as user_name,
        u.email as user_email,
        u.avatar_url as user_avatar
      FROM candidate_notes cn
      LEFT JOIN users u ON cn.user_id = u.id
      WHERE cn.id = $1
    `, [result.rows[0].id]);

    res.status(201).json(noteWithUser.rows[0]);
  } catch (error) {
    console.error('Error creating candidate note:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Update a client note
app.put('/api/client-notes/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { content, user_id } = req.body;

    // Validate required fields
    if (!content || !user_id) {
      return res.status(400).json({
        error: 'Missing required fields: content and user_id are required'
      });
    }

    // Verify note exists and user owns it
    const noteCheck = await query(
      'SELECT id, user_id FROM client_notes WHERE id = $1',
      [id]
    );

    if (noteCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Note not found' });
    }

    if (noteCheck.rows[0].user_id !== user_id) {
      return res.status(403).json({ error: 'Not authorized to update this note' });
    }

    // Update the note
    const queryBuilder = new QueryBuilder('client_notes');
    queryBuilder.where('id', '=', id);

    const { text, params } = queryBuilder.buildUpdate({
      content,
      updated_at: new Date()
    });

    await query(text, params);

    // Return the updated note with user information
    const updatedNote = await query(`
      SELECT
        cn.id,
        cn.client_id,
        cn.user_id,
        cn.content,
        cn.created_at,
        cn.updated_at,
        u.first_name || ' ' || COALESCE(u.last_name, '') as user_name,
        u.email as user_email,
        u.avatar_url as user_avatar
      FROM client_notes cn
      LEFT JOIN users u ON cn.user_id = u.id
      WHERE cn.id = $1
    `, [id]);

    res.json(updatedNote.rows[0]);
  } catch (error) {
    console.error('Error updating client note:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Delete a client note
app.delete('/api/client-notes/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { user_id } = req.body;

    // Validate required fields
    if (!user_id) {
      return res.status(400).json({
        error: 'Missing required field: user_id is required'
      });
    }

    // Verify note exists and user owns it
    const noteCheck = await query(
      'SELECT id, user_id FROM client_notes WHERE id = $1',
      [id]
    );

    if (noteCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Note not found' });
    }

    if (noteCheck.rows[0].user_id !== user_id) {
      return res.status(403).json({ error: 'Not authorized to delete this note' });
    }

    // Delete the note
    await query('DELETE FROM client_notes WHERE id = $1', [id]);

    res.status(204).send();
  } catch (error) {
    console.error('Error deleting client note:', error);
    res.status(500).json({ error: 'Database error' });
  }
});

// Typesense client setup
const Typesense = require('typesense');

const typesenseClient = new Typesense.Client({
  nodes: [
    {
      host: process.env.VITE_TYPESENSE_HOST || 'typesense',
      port: parseInt(process.env.VITE_TYPESENSE_PORT || '8108'),
      protocol: process.env.VITE_TYPESENSE_PROTOCOL || 'http',
    },
  ],
  apiKey: process.env.VITE_TYPESENSE_API_KEY || 'xyz123',
  connectionTimeoutSeconds: 5,
  retryIntervalSeconds: 1,
  numRetries: 3,
});

// Search analytics logging
const logSearchQuery = (query, results, searchTime, userAgent = '') => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`Search Analytics: "${query}" - ${results.found} results in ${searchTime}ms`);
  }
  // In production, you could log to a file or analytics service
  // Example: analytics.track('search', { query, results: results.found, searchTime });
};

// Search endpoints
app.get('/api/search', async (req, res) => {
  const startTime = Date.now();
  try {
    const {
      q: query,
      page = 1,
      per_page: perPage = 10,
      filter_by: filterBy,
      sort_by: sortBy = '_text_match:desc',
      group_by: groupBy = 'entity_type',
      group_limit: groupLimit = 3
    } = req.query;

    if (!query || query.trim() === '') {
      return res.json({
        found: 0,
        hits: [],
        page: parseInt(page),
        search_time_ms: 0,
      });
    }

    const searchParameters = {
      q: query,
      query_by: 'title,description,name,company_name,contact_name,skills',
      filter_by: filterBy || '',
      sort_by: sortBy,
      page: parseInt(page),
      per_page: parseInt(perPage),
      group_by: groupBy,
      group_limit: parseInt(groupLimit),
    };

    // Perform multi-collection search - search each collection separately with appropriate fields
    const searchResults = await typesenseClient.multiSearch.perform({
      searches: [
        {
          collection: 'jobs',
          q: query,
          query_by: 'title,description,company',
          filter_by: filterBy || '',
          sort_by: sortBy,
          page: parseInt(page),
          per_page: parseInt(groupLimit), // Use groupLimit for individual searches
        },
        {
          collection: 'candidates',
          q: query,
          query_by: 'title,description,name,email',
          filter_by: filterBy || '',
          sort_by: sortBy,
          page: parseInt(page),
          per_page: parseInt(groupLimit), // Use groupLimit for individual searches
        },
        {
          collection: 'clients',
          q: query,
          query_by: 'title,description,company_name,contact_name,email',
          filter_by: filterBy || '',
          sort_by: sortBy,
          page: parseInt(page),
          per_page: parseInt(groupLimit), // Use groupLimit for individual searches
        },
        {
          collection: 'settings',
          q: query,
          query_by: 'title,description,section,category,keywords',
          filter_by: filterBy || '',
          sort_by: sortBy,
          page: parseInt(page),
          per_page: parseInt(groupLimit), // Use groupLimit for individual searches
        },
        {
          collection: 'events',
          q: query,
          query_by: 'title,description,event_type,location,participants',
          filter_by: filterBy || '',
          sort_by: sortBy,
          page: parseInt(page),
          per_page: parseInt(groupLimit), // Use groupLimit for individual searches
        },
        {
          collection: 'messages',
          q: query,
          query_by: 'title,description,sender,recipient,message_type,tags',
          filter_by: filterBy || '',
          sort_by: sortBy,
          page: parseInt(page),
          per_page: parseInt(groupLimit), // Use groupLimit for individual searches
        },
        {
          collection: 'notifications',
          q: query,
          query_by: 'title,description,notification_type,category,recipient',
          filter_by: filterBy || '',
          sort_by: sortBy,
          page: parseInt(page),
          per_page: parseInt(groupLimit), // Use groupLimit for individual searches
        },
      ],
    });

    // Process and format results from multiple collections
    const allResults = searchResults?.results || [];
    const collections = ['jobs', 'candidates', 'clients', 'settings', 'events', 'messages', 'notifications'];

    let totalFound = 0;
    let totalSearchTime = 0;
    const groupedHits = {};

    // Process each collection's results
    allResults.forEach((result, index) => {
      const collectionName = collections[index];
      if (result && result.hits && Array.isArray(result.hits)) {
        const hits = result.hits
          .filter(hit => hit && hit.document)
          .map((hit) => hit.document); // Already limited by per_page in search

        if (hits.length > 0) {
          groupedHits[collectionName] = {
            count: result.found || 0,
            hits,
          };
        }
      }

      totalFound += result?.found || 0;
      totalSearchTime += result?.search_time_ms || 0;
    });

    const formattedResults = {
      found: totalFound,
      page: parseInt(page),
      search_time_ms: totalSearchTime,
      request_params: searchParameters,
      grouped_hits: groupedHits,
    };

    // Log search analytics
    const endTime = Date.now();
    const totalRequestTime = endTime - startTime;
    logSearchQuery(query, formattedResults, totalRequestTime, req.get('User-Agent'));

    res.json(formattedResults);
  } catch (error) {
    console.error('Search error:', error);
    res.json({
      found: 0,
      hits: [],
      page: parseInt(req.query.page || 1),
      search_time_ms: 0,
      error: 'Search temporarily unavailable',
    });
  }
});

// Search health check endpoint
app.get('/api/search/health', async (req, res) => {
  try {
    const startTime = Date.now();

    // Test Typesense connection
    const collections = await typesenseClient.collections().retrieve();
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    // Check if required collections exist
    const requiredCollections = ['jobs', 'candidates', 'clients', 'settings', 'events', 'messages', 'notifications'];
    const existingCollections = collections.map(c => c.name);
    const missingCollections = requiredCollections.filter(c => !existingCollections.includes(c));

    const isHealthy = missingCollections.length === 0 && responseTime < 1000;

    res.status(isHealthy ? 200 : 503).json({
      status: isHealthy ? 'healthy' : 'unhealthy',
      responseTime,
      collections: existingCollections.length,
      missingCollections,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: 'Typesense connection failed',
      timestamp: new Date().toISOString(),
    });
  }
});

// Search specific collection
app.get('/api/search/:collection', async (req, res) => {
  const startTime = Date.now();
  try {
    const { collection } = req.params;
    const {
      q: query,
      page = 1,
      per_page: perPage = 10,
      filter_by: filterBy,
      sort_by: sortBy = '_text_match:desc'
    } = req.query;

    if (!query || query.trim() === '') {
      return res.json({
        found: 0,
        hits: [],
        page: parseInt(page),
        search_time_ms: 0,
      });
    }

    // Define query fields based on collection
    let queryBy;
    switch (collection) {
      case 'jobs':
        queryBy = 'title,description,company';
        break;
      case 'candidates':
        queryBy = 'title,description,name,email';
        break;
      case 'clients':
        queryBy = 'title,description,company_name,contact_name,email';
        break;
      case 'settings':
        queryBy = 'title,description,section,category,keywords';
        break;
      case 'events':
        queryBy = 'title,description,event_type,location,participants';
        break;
      case 'messages':
        queryBy = 'title,description,sender,recipient,message_type,tags';
        break;
      case 'notifications':
        queryBy = 'title,description,notification_type,category,recipient';
        break;
      default:
        queryBy = 'title,description';
    }

    const searchParameters = {
      q: query,
      query_by: queryBy,
      filter_by: filterBy || '',
      sort_by: sortBy,
      page: parseInt(page),
      per_page: parseInt(perPage),
    };

    const results = await typesenseClient
      .collections(collection)
      .documents()
      .search(searchParameters);

    const formattedResults = {
      found: results?.found || 0,
      page: parseInt(page),
      search_time_ms: results?.search_time_ms || 0,
      request_params: searchParameters,
    };

    if (results?.hits && Array.isArray(results.hits)) {
      formattedResults.hits = results.hits
        .filter(hit => hit && hit.document)
        .map((hit) => hit.document);
    } else {
      formattedResults.hits = [];
    }

    // Log search analytics
    const endTime = Date.now();
    const totalRequestTime = endTime - startTime;
    logSearchQuery(`${collection}:${query}`, formattedResults, totalRequestTime, req.get('User-Agent'));

    res.json(formattedResults);
  } catch (error) {
    console.error(`Search error in ${req.params.collection}:`, error);
    res.json({
      found: 0,
      hits: [],
      page: parseInt(req.query.page || 1),
      search_time_ms: 0,
      error: 'Search temporarily unavailable',
    });
  }
});

// Data synchronization endpoints
app.post('/api/search/sync/:collection', async (req, res) => {
  const startTime = Date.now();
  try {
    const { collection } = req.params;
    let syncCount = 0;

    switch (collection) {
      case 'jobs':
        const jobsResult = await query(`
          SELECT
            j.id,
            j.title,
            COALESCE(j.description, '') as description,
            COALESCE(j.location, '') as location,
            CASE
              WHEN j.salary_min IS NOT NULL AND j.salary_max IS NOT NULL
              THEN CONCAT(j.salary_min, ' - ', j.salary_max, ' ', j.salary_currency)
              ELSE ''
            END as salary_range,
            COALESCE(j.employment_type, '') as job_type,
            j.status,
            EXTRACT(EPOCH FROM j.created_at) * 1000 as created_at,
            EXTRACT(EPOCH FROM j.updated_at) * 1000 as updated_at,
            COALESCE(c.company_name, '') as company
          FROM jobs j
          LEFT JOIN clients c ON c.id = j.client_id
        `);

        const jobDocuments = jobsResult.rows.map(job => ({
          id: job.id,
          title: job.title || '',
          description: job.description || '',
          entity_type: 'jobs',
          created_at: parseInt(job.created_at) || Date.now(),
          updated_at: parseInt(job.updated_at) || Date.now(),
          company: job.company || '',
          location: job.location || '',
          salary_range: job.salary_range || '',
          job_type: job.job_type || '',
          status: job.status || 'open',
          skills: [], // Add skills if available in your schema
        }));

        if (jobDocuments.length > 0) {
          await typesenseClient.collections('jobs').documents().import(jobDocuments, { action: 'upsert' });
          syncCount = jobDocuments.length;
        }
        break;

      case 'candidates':
        const candidatesResult = await query(`
          SELECT
            id,
            CONCAT(first_name, ' ', COALESCE(last_name, '')) as name,
            CONCAT(first_name, ' ', COALESCE(last_name, '')) as title,
            COALESCE(email, '') as email,
            COALESCE(phone, '') as phone,
            COALESCE(location, '') as location,
            skills,
            experience_years,
            status,
            secondary_status,
            EXTRACT(EPOCH FROM created_at) * 1000 as created_at,
            EXTRACT(EPOCH FROM updated_at) * 1000 as updated_at,
            CONCAT('Candidate with ', COALESCE(experience_years, 0), ' years of experience') as description
          FROM candidates
        `);

        const candidateDocuments = candidatesResult.rows.map(candidate => ({
          id: candidate.id,
          title: candidate.title || '',
          description: candidate.description || '',
          entity_type: 'candidates',
          created_at: parseInt(candidate.created_at) || Date.now(),
          updated_at: parseInt(candidate.updated_at) || Date.now(),
          name: candidate.name || '',
          email: candidate.email || '',
          phone: candidate.phone || '',
          location: candidate.location || '',
          skills: Array.isArray(candidate.skills) ? candidate.skills : [],
          experience_years: candidate.experience_years || 0,
          status: candidate.status || 'new',
          secondary_status: candidate.secondary_status || '',
        }));

        if (candidateDocuments.length > 0) {
          await typesenseClient.collections('candidates').documents().import(candidateDocuments, { action: 'upsert' });
          syncCount = candidateDocuments.length;
        }
        break;

      case 'clients':
        const clientsResult = await query(`
          SELECT
            id,
            company_name,
            company_name as title,
            COALESCE(email, '') as email,
            COALESCE(phone, '') as phone,
            COALESCE(industry, '') as industry,
            CONCAT(COALESCE(city, ''),
                   CASE WHEN city IS NOT NULL AND state IS NOT NULL THEN ', ' ELSE '' END,
                   COALESCE(state, ''),
                   CASE WHEN (city IS NOT NULL OR state IS NOT NULL) AND country IS NOT NULL THEN ', ' ELSE '' END,
                   COALESCE(country, '')) as location,
            EXTRACT(EPOCH FROM created_at) * 1000 as created_at,
            EXTRACT(EPOCH FROM updated_at) * 1000 as updated_at,
            CONCAT('Company in ', COALESCE(industry, 'various'), ' industry') as description
          FROM clients
        `);

        const clientDocuments = clientsResult.rows.map(client => ({
          id: client.id,
          title: client.title || '',
          description: client.description || '',
          entity_type: 'clients',
          created_at: parseInt(client.created_at) || Date.now(),
          updated_at: parseInt(client.updated_at) || Date.now(),
          company_name: client.company_name || '',
          contact_name: 'Contact Person', // Default value since we don't have this field
          email: client.email || '',
          phone: client.phone || '',
          industry: client.industry || '',
          location: client.location || '',
        }));

        if (clientDocuments.length > 0) {
          await typesenseClient.collections('clients').documents().import(clientDocuments, { action: 'upsert' });
          syncCount = clientDocuments.length;
        }
        break;

      case 'settings':
        // Define settings content to be indexed
        const settingsData = [
          // Main settings sections
          {
            id: 'settings-profile',
            title: 'Profile Settings',
            description: 'Manage your personal profile information and preferences',
            section: 'profile',
            category: 'personal',
            setting_type: 'page',
            route: '/settings',
            keywords: ['profile', 'personal', 'info', 'user', 'account'],
          },
          {
            id: 'settings-user',
            title: 'User Settings',
            description: 'Customize your experience with themes and accessibility options',
            section: 'user',
            category: 'preferences',
            setting_type: 'page',
            route: '/settings',
            keywords: ['theme', 'dark', 'light', 'accessibility', 'preferences', 'appearance'],
          },
          {
            id: 'settings-notifications',
            title: 'Notification Settings',
            description: 'Configure how you receive notifications and alerts',
            section: 'notifications',
            category: 'communication',
            setting_type: 'page',
            route: '/settings',
            keywords: ['notifications', 'alerts', 'email', 'push', 'reminders'],
          },
          {
            id: 'settings-smtp',
            title: 'SMTP Settings',
            description: 'Configure email server settings for outgoing messages',
            section: 'smtp',
            category: 'communication',
            setting_type: 'page',
            route: '/settings',
            keywords: ['smtp', 'email', 'server', 'mail', 'outgoing'],
          },
          {
            id: 'settings-phone',
            title: 'Phone Services',
            description: 'Configure SMS, WhatsApp, and voice communication services',
            section: 'phone',
            category: 'communication',
            setting_type: 'page',
            route: '/settings',
            keywords: ['phone', 'sms', 'whatsapp', 'voice', 'twilio', 'communication'],
          },
          {
            id: 'settings-integrations',
            title: 'Integrations',
            description: 'Manage AI integrations and third-party service connections',
            section: 'integrations',
            category: 'external',
            setting_type: 'page',
            route: '/settings',
            keywords: ['integrations', 'ai', 'api', 'third-party', 'connections', 'openai'],
          },
          {
            id: 'settings-security',
            title: 'Security',
            description: 'Manage security settings and access controls',
            section: 'security',
            category: 'security',
            setting_type: 'page',
            route: '/settings',
            keywords: ['security', 'password', 'access', 'authentication', 'privacy'],
          },
          {
            id: 'settings-company',
            title: 'Company Settings',
            description: 'Configure company-wide settings and preferences',
            section: 'company',
            category: 'organization',
            setting_type: 'page',
            route: '/settings',
            keywords: ['company', 'organization', 'business', 'corporate'],
          },
          {
            id: 'settings-team',
            title: 'Team Members',
            description: 'Manage team members and user permissions',
            section: 'team',
            category: 'organization',
            setting_type: 'page',
            route: '/settings',
            keywords: ['team', 'members', 'users', 'permissions', 'roles'],
          },
          {
            id: 'settings-emails',
            title: 'Email Templates',
            description: 'Customize email templates for automated communications',
            section: 'emails',
            category: 'communication',
            setting_type: 'page',
            route: '/settings',
            keywords: ['email', 'templates', 'automated', 'communication', 'messages'],
          },
        ];

        const settingsDocuments = settingsData.map(setting => ({
          ...setting,
          entity_type: 'settings',
          created_at: Date.now(),
          updated_at: Date.now(),
        }));

        if (settingsDocuments.length > 0) {
          await typesenseClient.collections('settings').documents().import(settingsDocuments, { action: 'upsert' });
          syncCount = settingsDocuments.length;
        }
        break;

      case 'events':
        // Define events dummy data
        const eventsData = [
          {
            id: 'event-1',
            title: 'Interview with John Smith',
            description: 'Technical interview for Senior Developer position',
            event_type: 'interview',
            start_date: Date.now() + (24 * 60 * 60 * 1000), // Tomorrow
            end_date: Date.now() + (24 * 60 * 60 * 1000) + (60 * 60 * 1000), // Tomorrow + 1 hour
            location: 'Conference Room A',
            participants: ['John Smith', 'HR Manager', 'Tech Lead'],
            status: 'scheduled',
            priority: 'high',
          },
          {
            id: 'event-2',
            title: 'Team Standup Meeting',
            description: 'Daily standup meeting with development team',
            event_type: 'meeting',
            start_date: Date.now() + (60 * 60 * 1000), // In 1 hour
            end_date: Date.now() + (60 * 60 * 1000) + (30 * 60 * 1000), // In 1.5 hours
            location: 'Virtual - Zoom',
            participants: ['Development Team', 'Scrum Master'],
            status: 'scheduled',
            priority: 'medium',
          },
          {
            id: 'event-3',
            title: 'Project Deadline - Q1 Release',
            description: 'Final deadline for Q1 product release',
            event_type: 'deadline',
            start_date: Date.now() + (7 * 24 * 60 * 60 * 1000), // In 1 week
            location: 'N/A',
            participants: ['Product Team', 'Development Team'],
            status: 'scheduled',
            priority: 'high',
          },
          {
            id: 'event-4',
            title: 'Client Presentation',
            description: 'Quarterly business review with Acme Corp',
            event_type: 'meeting',
            start_date: Date.now() + (3 * 24 * 60 * 60 * 1000), // In 3 days
            end_date: Date.now() + (3 * 24 * 60 * 60 * 1000) + (2 * 60 * 60 * 1000), // In 3 days + 2 hours
            location: 'Client Office',
            participants: ['Account Manager', 'Sales Team', 'Client'],
            status: 'scheduled',
            priority: 'high',
          },
          {
            id: 'event-5',
            title: 'Code Review Session',
            description: 'Weekly code review and architecture discussion',
            event_type: 'meeting',
            start_date: Date.now() + (2 * 24 * 60 * 60 * 1000), // In 2 days
            end_date: Date.now() + (2 * 24 * 60 * 60 * 1000) + (90 * 60 * 1000), // In 2 days + 1.5 hours
            location: 'Development Lab',
            participants: ['Senior Developers', 'Tech Lead'],
            status: 'scheduled',
            priority: 'medium',
          },
        ];

        const eventDocuments = eventsData.map(event => ({
          ...event,
          entity_type: 'events',
          created_at: Date.now(),
          updated_at: Date.now(),
        }));

        if (eventDocuments.length > 0) {
          await typesenseClient.collections('events').documents().import(eventDocuments, { action: 'upsert' });
          syncCount = eventDocuments.length;
        }
        break;

      case 'messages':
        // Define messages dummy data
        const messagesData = [
          {
            id: 'message-1',
            title: 'Welcome to the team!',
            description: 'Welcome message for new team member John Smith',
            sender: 'HR Manager',
            recipient: 'John Smith',
            message_type: 'internal',
            status: 'delivered',
            thread_id: 'thread-welcome-001',
            priority: 'medium',
            tags: ['welcome', 'onboarding', 'hr'],
          },
          {
            id: 'message-2',
            title: 'Interview Confirmation',
            description: 'Confirmation email for tomorrow\'s technical interview',
            sender: 'Recruitment Team',
            recipient: 'Jane Doe',
            message_type: 'email',
            status: 'sent',
            priority: 'high',
            tags: ['interview', 'confirmation', 'recruitment'],
          },
          {
            id: 'message-3',
            title: 'Project Update Required',
            description: 'Please provide status update on Q1 project milestones',
            sender: 'Project Manager',
            recipient: 'Development Team',
            message_type: 'internal',
            status: 'read',
            thread_id: 'thread-project-q1',
            priority: 'high',
            tags: ['project', 'update', 'milestone'],
          },
          {
            id: 'message-4',
            title: 'Client Feedback Received',
            description: 'Positive feedback from Acme Corp on recent deliverables',
            sender: 'Account Manager',
            recipient: 'Project Team',
            message_type: 'internal',
            status: 'read',
            priority: 'medium',
            tags: ['client', 'feedback', 'positive'],
          },
          {
            id: 'message-5',
            title: 'System Maintenance Notice',
            description: 'Scheduled maintenance window this weekend',
            sender: 'IT Operations',
            recipient: 'All Users',
            message_type: 'announcement',
            status: 'delivered',
            priority: 'medium',
            tags: ['maintenance', 'system', 'announcement'],
          },
          {
            id: 'message-6',
            title: 'Code Review Comments',
            description: 'Comments and suggestions on recent pull request',
            sender: 'Senior Developer',
            recipient: 'Junior Developer',
            message_type: 'internal',
            status: 'read',
            thread_id: 'thread-code-review-pr123',
            priority: 'medium',
            tags: ['code-review', 'development', 'feedback'],
          },
        ];

        const messageDocuments = messagesData.map(message => ({
          ...message,
          entity_type: 'messages',
          created_at: Math.floor(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // Random time in last week
          updated_at: Date.now(),
        }));

        if (messageDocuments.length > 0) {
          await typesenseClient.collections('messages').documents().import(messageDocuments, { action: 'upsert' });
          syncCount = messageDocuments.length;
        }
        break;

      case 'notifications':
        // Define notifications dummy data
        const notificationsData = [
          {
            id: 'notification-1',
            title: 'New Job Application',
            description: 'John Smith applied for Senior Developer position',
            notification_type: 'alert',
            priority: 'high',
            status: 'unread',
            recipient: 'HR Manager',
            action_url: '/candidates/john-smith-001',
            category: 'candidate',
            expires_at: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days from now
          },
          {
            id: 'notification-2',
            title: 'Interview Reminder',
            description: 'Technical interview with Jane Doe in 1 hour',
            notification_type: 'reminder',
            priority: 'high',
            status: 'unread',
            recipient: 'Tech Lead',
            action_url: '/calendar/interview-jane-doe',
            category: 'interview',
            expires_at: Date.now() + (2 * 60 * 60 * 1000), // 2 hours from now
          },
          {
            id: 'notification-3',
            title: 'System Update Complete',
            description: 'ATS system has been successfully updated to version 2.1',
            notification_type: 'system',
            priority: 'medium',
            status: 'read',
            recipient: 'All Users',
            category: 'system',
            expires_at: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 days from now
          },
          {
            id: 'notification-4',
            title: 'Client Contract Renewal',
            description: 'Acme Corp contract expires in 30 days',
            notification_type: 'reminder',
            priority: 'high',
            status: 'unread',
            recipient: 'Account Manager',
            action_url: '/clients/acme-corp',
            category: 'client',
            expires_at: Date.now() + (30 * 24 * 60 * 60 * 1000), // 30 days from now
          },
          {
            id: 'notification-5',
            title: 'Weekly Report Ready',
            description: 'Your weekly recruitment report is ready for review',
            notification_type: 'alert',
            priority: 'medium',
            status: 'unread',
            recipient: 'Recruitment Manager',
            action_url: '/reports/weekly',
            category: 'report',
            expires_at: Date.now() + (7 * 24 * 60 * 60 * 1000), // 7 days from now
          },
          {
            id: 'notification-6',
            title: 'Backup Completed',
            description: 'Daily system backup completed successfully',
            notification_type: 'system',
            priority: 'low',
            status: 'read',
            recipient: 'IT Admin',
            category: 'system',
            expires_at: Date.now() + (24 * 60 * 60 * 1000), // 24 hours from now
          },
        ];

        const notificationDocuments = notificationsData.map(notification => ({
          ...notification,
          entity_type: 'notifications',
          created_at: Math.floor(Date.now() - Math.random() * 3 * 24 * 60 * 60 * 1000), // Random time in last 3 days
          updated_at: Date.now(),
        }));

        if (notificationDocuments.length > 0) {
          await typesenseClient.collections('notifications').documents().import(notificationDocuments, { action: 'upsert' });
          syncCount = notificationDocuments.length;
        }
        break;

      default:
        return res.status(400).json({ error: 'Invalid collection name' });
    }

    const endTime = Date.now();
    const syncTime = endTime - startTime;

    if (process.env.NODE_ENV === 'development') {
      console.log(`Sync Analytics: ${collection} - ${syncCount} documents synced in ${syncTime}ms`);
    }

    res.json({
      success: true,
      collection,
      syncCount,
      syncTime,
      message: `Successfully synced ${syncCount} ${collection} to search index`,
    });
  } catch (error) {
    console.error(`Error syncing ${req.params.collection}:`, error);
    res.status(500).json({
      success: false,
      error: 'Failed to sync data to search index',
      message: error.message,
    });
  }
});

// Sync all collections
app.post('/api/search/sync', async (req, res) => {
  try {
    const collections = ['jobs', 'candidates', 'clients', 'settings', 'events', 'messages', 'notifications'];
    const results = {};

    for (const collection of collections) {
      try {
        const syncResponse = await fetch(`http://localhost:${PORT}/api/search/sync/${collection}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
        });
        const syncResult = await syncResponse.json();
        results[collection] = syncResult;
      } catch (error) {
        results[collection] = { success: false, error: error.message };
      }
    }

    res.json({
      success: true,
      message: 'Sync completed for all collections',
      results,
    });
  } catch (error) {
    console.error('Error syncing all collections:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to sync all collections',
      message: error.message,
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;
