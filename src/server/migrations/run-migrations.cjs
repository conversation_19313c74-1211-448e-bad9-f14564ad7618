/**
 * Database Migration Runner
 *
 * This script runs SQL migration files to update the database schema.
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// Create a PostgreSQL connection pool
const pool = new Pool({
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  database: process.env.POSTGRES_DATABASE || 'postgres',
  user: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD || 'postgres',
  ssl: process.env.POSTGRES_SSL === 'true'
});

// Migration files to run in order
const migrations = [
  'add_secondary_status.sql',
  'update_status_mapping.sql',
  'add_incubator_status.sql',
  'update_secondary_status_constraint.sql',
  'add_candidate_notes_table.sql',
  'add_default_user.sql',
  'enforce_job_client_relationship.sql'
];

async function runMigrations() {
  if (process.env.NODE_ENV === 'development') {
    console.log('Running database migrations...');
  }

  try {
    // Create migrations table if it doesn't exist
    await pool.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Get already applied migrations
    const { rows } = await pool.query('SELECT name FROM migrations');
    const appliedMigrations = rows.map(row => row.name);

    // Run each migration that hasn't been applied yet
    for (const migration of migrations) {
      if (appliedMigrations.includes(migration)) {
        console.log(`Migration ${migration} already applied, skipping...`);
        continue;
      }

      if (process.env.NODE_ENV === 'development') {
        console.log(`Applying migration: ${migration}`);
      }

      // Read and execute the migration file
      const migrationPath = path.join(__dirname, migration);
      const migrationSql = fs.readFileSync(migrationPath, 'utf8');

      // Start a transaction
      const client = await pool.connect();
      try {
        await client.query('BEGIN');

        // Run the migration
        await client.query(migrationSql);

        // Record the migration
        await client.query(
          'INSERT INTO migrations (name) VALUES ($1)',
          [migration]
        );

        await client.query('COMMIT');
        if (process.env.NODE_ENV === 'development') {
          console.log(`Migration ${migration} applied successfully`);
        }
      } catch (error) {
        await client.query('ROLLBACK');
        if (process.env.NODE_ENV === 'development') {
          console.error(`Error applying migration ${migration}:`, error);
        }
        throw error;
      } finally {
        client.release();
      }
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('All migrations completed successfully');
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Migration process failed:', error);
    }
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the migrations
runMigrations();
