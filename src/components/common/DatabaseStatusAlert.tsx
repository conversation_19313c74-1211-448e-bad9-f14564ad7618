import React, { useState, useEffect, memo, useMemo } from 'react';
import { AlertCircle, RefreshCw, Database } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { useDatabaseStatus } from '@/hooks/useDatabaseStatus';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface DatabaseStatusAlertProps {
  className?: string;
}

export const DatabaseStatusAlert = memo(function DatabaseStatusAlert({ className }: DatabaseStatusAlertProps) {
  const { isConnected, isChecking, connectionStatus } = useDatabaseStatus();
  const [isOpen, setIsOpen] = useState(false);
  const [dismissed, setDismissed] = useState(false);

  // Reset dismissed state when connection status changes
  useEffect(() => {
    if (!isConnected) {
      setDismissed(false);
    }
  }, [isConnected]);

  // Memoize the formatted time to prevent unnecessary recalculations
  const formattedTime = useMemo(() => {
    if (!connectionStatus?.lastChecked) return 'Unknown';
    return new Date(connectionStatus.lastChecked).toLocaleTimeString();
  }, [connectionStatus?.lastChecked]);

  // Memoize the visibility check
  const shouldShow = useMemo(() => {
    return !((isConnected && !isChecking) || dismissed);
  }, [isConnected, isChecking, dismissed]);

  // Don't show anything if connected or if the user dismissed the alert
  if (!shouldShow) {
    return null;
  }

  return (
    <Alert
      variant={isChecking ? "default" : "destructive"}
      className={`mb-4 ${className}`}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-2">
          {isChecking ? (
            <RefreshCw className="h-4 w-4 animate-spin mt-1" />
          ) : (
            <AlertCircle className="h-4 w-4 mt-1" />
          )}
          <div>
            <AlertTitle className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              {isChecking ? "Checking database connection..." : "Database Connection Issue"}
            </AlertTitle>
            <AlertDescription>
              {isChecking
                ? "Please wait while we verify the database connection."
                : "There was a problem connecting to the database. This may affect your ability to view or save data."
              }
            </AlertDescription>
          </div>
        </div>
        {!isChecking && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setDismissed(true)}
          >
            Dismiss
          </Button>
        )}
      </div>

      <Collapsible open={isOpen} onOpenChange={setIsOpen} className="mt-2">
        <CollapsibleTrigger asChild>
          <Button variant="outline" size="sm" className="w-full mt-2">
            {isOpen ? "Hide Details" : "Show Troubleshooting Steps"}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-2 space-y-2 text-sm">
          <div className="rounded-md bg-muted p-3 space-y-2">
            <h4 className="font-medium">Connection Details:</h4>
            <p>Last checked: {formattedTime}</p>
            {connectionStatus?.error && (
              <p className="text-destructive">Error: {connectionStatus.error.message || 'Unknown error'}</p>
            )}

            <h4 className="font-medium mt-3">Troubleshooting Steps:</h4>
            <ol className="list-decimal list-inside space-y-1">
              <li>Check that the PostgreSQL database is running</li>
              <li>Verify that the database credentials in your .env file are correct</li>
              <li>Ensure the API server is running and can connect to the database</li>
              <li>Check network connectivity between the application and database</li>
              <li>Restart the application and database services</li>
            </ol>

            <div className="mt-3">
              <Button
                variant="default"
                size="sm"
                className="w-full"
                onClick={() => window.location.reload()}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Application
              </Button>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </Alert>
  );
});
