import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import { Dialog } from '@/components/ui/dialog';
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, Search, X, Briefcase, User, Building2, Settings, Calendar, MessageSquare, Bell } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import useSearch from '@/hooks/useSearch';
import { SearchResultItem as SearchResult } from '@/lib/search/search';
import { cn } from "@/lib/utils";

// Custom DialogContent without automatic close button
const CustomDialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <DialogPrimitive.Portal>
    <DialogPrimitive.Overlay className="fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
        className
      )}
      {...props}
    >
      {children}
    </DialogPrimitive.Content>
  </DialogPrimitive.Portal>
));
CustomDialogContent.displayName = "CustomDialogContent";

// Componente memoizado para mostrar un resultado de búsqueda individual con mejor hover y soporte para navegación por teclado
const SearchResultItem = memo(({
  hit,
  onClick,
  isFocused = false,
  onKeyDown,
  tabIndex = -1,
  id
}: {
  hit: SearchResult;
  onClick: (hit: SearchResult) => void;
  isFocused?: boolean;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  tabIndex?: number;
  id?: string;
}) => {
  return (
    <div
      id={id}
      role="option"
      aria-selected={isFocused}
      tabIndex={tabIndex}
      className={`flex items-center px-3 py-2.5 rounded-md cursor-pointer transition-all duration-150 border ${
        isFocused
          ? 'bg-accent border-ring shadow-sm ring-2 ring-ring/20'
          : 'border-transparent hover:bg-accent/50 hover:border-border/50 hover:shadow-sm'
      }`}
      onClick={() => onClick(hit)}
      onKeyDown={onKeyDown}
    >
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium truncate text-foreground">
          {hit.title || hit.name || hit.company_name}
        </p>
        {hit.description && (
          <p className="text-xs text-muted-foreground truncate mt-0.5">
            {hit.description}
          </p>
        )}
      </div>
    </div>
  );
});

// Componente memoizado para mostrar un grupo de resultados con mejor distinción visual
const SearchResultGroup = memo(({
  entityType,
  group,
  onResultClick,
  isFirst = false,
  focusedIndex,
  onItemKeyDown,
  startIndex = 0
}: {
  entityType: string;
  group: { count: number; hits: SearchResult[] };
  onResultClick: (hit: SearchResult) => void;
  isFirst?: boolean;
  focusedIndex?: number;
  onItemKeyDown?: (e: React.KeyboardEvent) => void;
  startIndex?: number;
}) => {
  // Get entity icon with enhanced styling
  const getEntityIcon = (entityType: string) => {
    switch (entityType) {
      case 'jobs':
        return <Briefcase className="h-4 w-4 mr-3 text-[#2662d9]" />;
      case 'candidates':
        return <User className="h-4 w-4 mr-3 text-[#2eb88a]" />;
      case 'clients':
        return <Building2 className="h-4 w-4 mr-3 text-[#e88c30]" />;
      case 'settings':
        return <Settings className="h-4 w-4 mr-3 text-[#af57db]" />;
      case 'events':
        return <Calendar className="h-4 w-4 mr-3 text-[#e23670]" />;
      case 'messages':
        return <MessageSquare className="h-4 w-4 mr-3 text-[#ebbf5e]" />;
      case 'notifications':
        return <Bell className="h-4 w-4 mr-3 text-[#54d3de]" />;
      default:
        return null;
    }
  };

  // Get entity label
  const getEntityLabel = (entityType: string) => {
    switch (entityType) {
      case 'jobs':
        return 'Jobs';
      case 'candidates':
        return 'Candidates';
      case 'clients':
        return 'Clients';
      case 'settings':
        return 'Settings';
      case 'events':
        return 'Events';
      case 'messages':
        return 'Messages';
      case 'notifications':
        return 'Notifications';
      default:
        return entityType;
    }
  };

  // Get entity-specific accent color for the border
  const getEntityAccentColor = (entityType: string) => {
    switch (entityType) {
      case 'jobs':
        return 'border-l-[#2662d9]';
      case 'candidates':
        return 'border-l-[#2eb88a]';
      case 'clients':
        return 'border-l-[#e88c30]';
      case 'settings':
        return 'border-l-[#af57db]';
      case 'events':
        return 'border-l-[#e23670]';
      case 'messages':
        return 'border-l-[#ebbf5e]';
      case 'notifications':
        return 'border-l-[#54d3de]';
      default:
        return 'border-l-border';
    }
  };

  return (
    <div className={`${!isFirst ? 'mt-6' : ''} mb-4`}>
      {/* Enhanced group header with visual separation */}
      <div className={`flex items-center justify-between mb-3 pb-2 border-l-4 pl-3 ${getEntityAccentColor(entityType)} bg-muted/30 rounded-r-md`}>
        <h3 className="text-sm font-semibold flex items-center text-foreground">
          {getEntityIcon(entityType)}
          {getEntityLabel(entityType)}
        </h3>
        <span className="text-xs font-medium text-muted-foreground bg-background px-2 py-1 rounded-full">
          {group.count} {group.count === 1 ? 'result' : 'results'}
        </span>
      </div>

      {/* Results with enhanced spacing */}
      <div className="space-y-1 pl-3" role="listbox">
        {group.hits.map((hit, index) => {
          const globalIndex = startIndex + index;
          return (
            <SearchResultItem
              key={`${hit.entity_type}-${hit.id}`}
              id={`search-result-${globalIndex}`}
              hit={hit}
              onClick={onResultClick}
              isFocused={focusedIndex === globalIndex}
              onKeyDown={onItemKeyDown}
              tabIndex={focusedIndex === globalIndex ? 0 : -1}
            />
          );
        })}
      </div>
    </div>
  );
});

export default function GlobalSearch() {
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [focusedIndex, setFocusedIndex] = useState<number>(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  const {
    query,
    setQuery,
    results,
    isLoading,
    resetSearch,
  } = useSearch({
    debounceMs: 200,
    perPage: 5,
    groupBy: 'entity_type',
  });

  // Get all results as a flat array for keyboard navigation
  const getAllResults = useCallback(() => {
    if (!results?.grouped_hits) return [];

    const allResults: SearchResult[] = [];
    Object.values(results.grouped_hits).forEach(group => {
      allResults.push(...group.hits);
    });
    return allResults;
  }, [results]);

  // Get results for current tab
  const getCurrentTabResults = useCallback(() => {
    if (!results?.grouped_hits) return [];

    if (activeTab === 'all') {
      return getAllResults();
    }

    const group = results.grouped_hits[activeTab];
    return group ? group.hits : [];
  }, [results, activeTab, getAllResults]);

  // Reset focused index when results change or tab changes
  useEffect(() => {
    setFocusedIndex(-1);
  }, [results, activeTab]);

  // Reset focused index when dialog closes
  useEffect(() => {
    if (!open) {
      setFocusedIndex(-1);
    }
  }, [open]);

  // Handle result click - memoizado para evitar recreaciones
  const handleResultClick = useCallback((result: SearchResult) => {
    setOpen(false);

    // Navigate based on entity type
    switch (result.entity_type) {
      case 'jobs':
        navigate(`/jobs/${result.id}`);
        break;
      case 'candidates':
        navigate(`/candidates/${result.id}`);
        break;
      case 'clients':
        navigate(`/clients/${result.id}`);
        break;
      case 'settings':
        // For settings, use the route field to navigate to specific settings page/section
        navigate(result.route || '/settings');
        break;
      case 'events':
        // Placeholder route for events - will be implemented later
        navigate(result.action_url || '/calendar');
        break;
      case 'messages':
        // Placeholder route for messages - will be implemented later
        navigate(result.action_url || '/messages');
        break;
      case 'notifications':
        // Placeholder route for notifications - will be implemented later
        navigate(result.action_url || '/notifications');
        break;
      default:
        console.warn(`Unknown entity type: ${result.entity_type}`);
    }
  }, [navigate]);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    const currentResults = getCurrentTabResults();

    if (currentResults.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setFocusedIndex(prev => {
          const newIndex = prev < currentResults.length - 1 ? prev + 1 : 0;
          // Focus the element after state update
          setTimeout(() => {
            const element = document.getElementById(`search-result-${newIndex}`);
            element?.focus();
          }, 0);
          return newIndex;
        });
        break;

      case 'ArrowUp':
        e.preventDefault();
        setFocusedIndex(prev => {
          const newIndex = prev > 0 ? prev - 1 : currentResults.length - 1;
          // Focus the element after state update
          setTimeout(() => {
            const element = document.getElementById(`search-result-${newIndex}`);
            element?.focus();
          }, 0);
          return newIndex;
        });
        break;

      case 'Enter':
        e.preventDefault();
        if (focusedIndex >= 0 && focusedIndex < currentResults.length) {
          handleResultClick(currentResults[focusedIndex]);
        }
        break;

      case 'Escape':
        e.preventDefault();
        setOpen(false);
        break;
    }
  }, [getCurrentTabResults, focusedIndex, handleResultClick]);

  // Handle keyboard navigation on individual items
  const handleItemKeyDown = useCallback((e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault();
        // The click handler will be called by the focused item
        break;
      default:
        // Delegate other keys to main handler
        handleKeyDown(e);
        break;
    }
  }, [handleKeyDown]);

  // Handle keyboard shortcut to open search (Ctrl+K or Cmd+K)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        setOpen(true);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Focus input when dialog opens
  useEffect(() => {
    if (open && inputRef.current) {
      // Usar requestAnimationFrame en lugar de setTimeout para mejor rendimiento
      requestAnimationFrame(() => {
        inputRef.current?.focus();
      });
    }
  }, [open]);

  // Reset search when dialog closes
  useEffect(() => {
    if (!open) {
      resetSearch();
    }
  }, [open, resetSearch]);

  return (
    <>
      {/* Keyboard shortcut indicator */}
      <Button
        variant="outline"
        className="w-full justify-between text-muted-foreground"
        onClick={() => setOpen(true)}
      >
        <div className="flex items-center">
          <Search className="mr-2 h-4 w-4" />
          <span>Search...</span>
        </div>
        <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>

      {/* Search Dialog */}
      <Dialog open={open} onOpenChange={setOpen}>
        <CustomDialogContent className="sm:max-w-[600px] p-0">
          <div className="flex items-center border-b px-3 py-1">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <Input
              ref={inputRef}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Search for anything..."
              className="flex h-12 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 border-0 focus-visible:ring-0"
              role="combobox"
              aria-expanded={query.trim() !== '' && results && results.found > 0}
              aria-haspopup="listbox"
              aria-autocomplete="list"
            />
            {query && (
              <Button
                variant="ghost"
                onClick={() => setQuery('')}
                className="h-6 w-6 p-0 rounded-md"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {query.trim() !== '' && (
            <div className="px-3 py-2">
              <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-2 grid w-full grid-cols-4 lg:grid-cols-8">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="jobs">Jobs</TabsTrigger>
                  <TabsTrigger value="candidates">Candidates</TabsTrigger>
                  <TabsTrigger value="clients">Clients</TabsTrigger>
                  <TabsTrigger value="settings">Settings</TabsTrigger>
                  <TabsTrigger value="events">Events</TabsTrigger>
                  <TabsTrigger value="messages">Messages</TabsTrigger>
                  <TabsTrigger value="notifications">Notifications</TabsTrigger>
                </TabsList>

                {isLoading ? (
                  <div className="flex justify-center py-6 mt-2 mb-4">
                    <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                  </div>
                ) : results && results.found > 0 ? (
                  <div className="mt-2 mb-4">
                    <TabsContent value="all" className="m-0">
                      <ScrollArea className="max-h-[300px]">
                        <div className="pr-3">
                          {results.grouped_hits && (() => {
                            let startIndex = 0;
                            return Object.entries(results.grouped_hits).map(([entityType, group], index) => {
                              const groupStartIndex = startIndex;
                              startIndex += group.hits.length;
                              return (
                                <SearchResultGroup
                                  key={entityType}
                                  entityType={entityType}
                                  group={group}
                                  onResultClick={handleResultClick}
                                  isFirst={index === 0}
                                  focusedIndex={focusedIndex}
                                  onItemKeyDown={handleItemKeyDown}
                                  startIndex={groupStartIndex}
                                />
                              );
                            });
                          })()}
                        </div>
                      </ScrollArea>
                    </TabsContent>

                    {['jobs', 'candidates', 'clients', 'settings', 'events', 'messages', 'notifications'].map((entityType) => (
                      <TabsContent key={entityType} value={entityType} className="m-0">
                        <ScrollArea className="max-h-[300px]">
                          {results.grouped_hits && results.grouped_hits[entityType] ? (
                            <div className="space-y-1 pr-3" role="listbox">
                              {results.grouped_hits[entityType].hits.map((hit, index) => (
                                <SearchResultItem
                                  key={`${hit.entity_type}-${hit.id}`}
                                  id={`search-result-${index}`}
                                  hit={hit}
                                  onClick={handleResultClick}
                                  isFocused={focusedIndex === index}
                                  onKeyDown={handleItemKeyDown}
                                  tabIndex={focusedIndex === index ? 0 : -1}
                                />
                              ))}
                            </div>
                          ) : (
                            <div className="py-6 text-center text-muted-foreground">
                              No {entityType} found
                            </div>
                          )}
                        </ScrollArea>
                      </TabsContent>
                    ))}
                  </div>
                ) : (
                  <div className="py-6 text-center mt-2 mb-4">
                    <p className="text-muted-foreground">No results found</p>
                  </div>
                )}
              </Tabs>
            </div>
          )}
        </CustomDialogContent>
      </Dialog>
    </>
  );
}
