
import { useState } from "react";
import { cn } from "@/lib/utils";
import {
  MoreHorizontal,
  Mail,
  Calendar,
  User
} from "lucide-react";
import { CandidateStatus } from "@/types/candidate";

interface CandidateProps {
  id: string;
  name: string;
  email: string;
  position: string;
  status: CandidateStatus;
  appliedDate: string;
  imageUrl?: string;
}

export default function CandidateItem({
  id,
  name,
  email,
  position,
  status,
  appliedDate,
  imageUrl,
}: CandidateProps) {
  const [isHovered, setIsHovered] = useState(false);

  const getStatusColor = (status: CandidateStatus) => {
    switch (status) {
      case "new":
        return "bg-blue-50 text-blue-700 border-blue-200";
      case "screening":
        return "bg-purple-50 text-purple-700 border-purple-200";
      case "interview":
        return "bg-amber-50 text-amber-700 border-amber-200";
      case "challenge":
        return "bg-orange-50 text-orange-700 border-orange-200";
      case "offer":
        return "bg-emerald-50 text-emerald-700 border-emerald-200";
      case "hired":
        return "bg-green-50 text-green-700 border-green-200";
      case "rejected":
        return "bg-red-50 text-red-700 border-red-200";
    }
  };

  return (
    <div 
      className="group relative flex items-center justify-between rounded-lg border border-border bg-card p-4 transition-all duration-200 hover:shadow-sm"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-center gap-4">
        <div className="h-10 w-10 overflow-hidden rounded-full bg-secondary flex items-center justify-center">
          {imageUrl ? (
            <img 
              src={imageUrl} 
              alt={name}
              className="h-full w-full object-cover"
            />
          ) : (
            <User className="h-5 w-5 text-muted-foreground" />
          )}
        </div>
        
        <div>
          <h3 className="font-medium">{name}</h3>
          <p className="text-sm text-muted-foreground">{position}</p>
        </div>
      </div>
      
      <div className="flex items-center gap-4">
        <div className={cn(
          "text-xs font-medium px-2.5 py-1 rounded-full border",
          getStatusColor(status)
        )}>
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </div>
        
        <div className="text-sm text-muted-foreground flex items-center gap-1">
          <Calendar className="h-3.5 w-3.5" />
          <span>{appliedDate}</span>
        </div>
        
        <div className={cn(
          "absolute right-4 flex gap-1 transition-opacity duration-200",
          isHovered ? "opacity-100" : "opacity-0"
        )}>
          <button className="rounded-full p-1.5 text-muted-foreground hover:bg-muted transition-colors">
            <Mail className="h-4 w-4" />
          </button>
          <button className="rounded-full p-1.5 text-muted-foreground hover:bg-muted transition-colors">
            <MoreHorizontal className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
