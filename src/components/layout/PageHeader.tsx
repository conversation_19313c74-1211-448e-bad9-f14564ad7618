import React, { ReactNode } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, List, LayoutGrid, RefreshCw, Upload, Download, Table } from 'lucide-react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface PageHeaderProps {
  title: string;
  description?: string;
  searchPlaceholder?: string;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  primaryAction?: {
    label: string;
    icon?: ReactNode;
    onClick?: () => void;
    href?: string;
  };
  viewMode?: string;
  onViewModeChange?: (mode: string) => void;
  showViewToggle?: boolean;
  onExport?: () => void;
  onImport?: (file: File) => Promise<void>;
  entityName?: string;
  children?: ReactNode;
  tabs?: {
    value: string;
    label: string;
  }[];
  activeTab?: string;
  onTabChange?: (value: string) => void;
  onRefresh?: () => Promise<void>;
  isRefreshing?: boolean;
}

export function PageHeader({
  title,
  description,
  searchPlaceholder = 'Search...',
  searchValue = '',
  onSearchChange,
  primaryAction,
  viewMode,
  onViewModeChange,
  showViewToggle = false,
  onExport,
  onImport,
  entityName,
  children,
  tabs,
  activeTab,
  onTabChange,
  onRefresh,
  isRefreshing = false
}: PageHeaderProps) {
  return (
    <div className="space-y-6">
      {/* Title and primary action */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold">{title}</h1>
          {description && <p className="text-muted-foreground mt-1">{description}</p>}
        </div>
        <div className="flex items-center gap-2">
          {onRefresh && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={onRefresh}
                    disabled={isRefreshing}
                    aria-label="Refresh data"
                  >
                    <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Refresh data</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {(onExport || onImport) && (
            <div className="flex items-center gap-2">
              {onImport && (
                <Button variant="outline" size="sm" onClick={() => onImport(new File([], 'dummy.csv'))}>
                  <Upload className="h-4 w-4 mr-2" />
                  Import CSV
                </Button>
              )}
              {onExport && (
                <Button variant="outline" size="sm" onClick={onExport}>
                  <Download className="h-4 w-4 mr-2" />
                  Export CSV
                </Button>
              )}
            </div>
          )}

          {/* View toggle without tabs */}
          {showViewToggle && !tabs && viewMode && onViewModeChange && (
            <div className="flex gap-2 p-1 rounded-lg bg-muted">
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => onViewModeChange("list")}
                aria-label="Switch to list view"
                title="Switch to list view"
              >
                <List className="h-4 w-4 mr-2" />
                List
              </Button>
              <Button
                variant={viewMode === "table" ? "default" : "ghost"}
                size="sm"
                onClick={() => onViewModeChange("table")}
                aria-label="Switch to table view"
                title="Switch to table view"
              >
                <Table className="h-4 w-4 mr-2" />
                Table
              </Button>
              <Button
                variant={viewMode === "kanban" ? "default" : "ghost"}
                size="sm"
                onClick={() => onViewModeChange("kanban")}
                aria-label="Switch to kanban view"
                title="Switch to kanban view"
              >
                <LayoutGrid className="h-4 w-4 mr-2" />
                Kanban
              </Button>
            </div>
          )}

          {primaryAction && (
            <Button className="gap-2" onClick={primaryAction.onClick} asChild={!!primaryAction.href}>
              {primaryAction.href ? (
                <a href={primaryAction.href}>
                  {primaryAction.icon}
                  <span>{primaryAction.label}</span>
                </a>
              ) : (
                <>
                  {primaryAction.icon}
                  <span>{primaryAction.label}</span>
                </>
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Search and filters */}
      {onSearchChange && (
        <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div className="relative w-full sm:w-96">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={searchPlaceholder}
              className="pl-8 border-muted-foreground/20"
              value={searchValue}
              onChange={(e) => onSearchChange(e.target.value)}
              aria-label={searchPlaceholder}
            />
          </div>
        </div>
      )}

      {/* Tabs */}
      {tabs && tabs.length > 0 && onTabChange && (
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mt-2">
          <Tabs defaultValue={tabs[0].value} value={activeTab} onValueChange={onTabChange} className="w-full sm:w-auto overflow-x-auto">
            <TabsList className="w-full sm:w-auto bg-background border border-muted-foreground/20 rounded-md">
              {tabs.map((tab) => (
                <TabsTrigger
                  key={tab.value}
                  value={tab.value}
                  className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>

          {(onExport || onImport) && showViewToggle && viewMode && onViewModeChange && (
            <div className="flex gap-2 p-1 rounded-lg bg-muted">
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => onViewModeChange("list")}
                aria-label="Switch to list view"
                title="Switch to list view"
              >
                <List className="h-4 w-4 mr-2" />
                List
              </Button>
              <Button
                variant={viewMode === "table" ? "default" : "ghost"}
                size="sm"
                onClick={() => onViewModeChange("table")}
                aria-label="Switch to table view"
                title="Switch to table view"
              >
                <Table className="h-4 w-4 mr-2" />
                Table
              </Button>
              <Button
                variant={viewMode === "kanban" ? "default" : "ghost"}
                size="sm"
                onClick={() => onViewModeChange("kanban")}
                aria-label="Switch to kanban view"
                title="Switch to kanban view"
              >
                <LayoutGrid className="h-4 w-4 mr-2" />
                Kanban
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Additional content */}
      {children}
    </div>
  );
}
