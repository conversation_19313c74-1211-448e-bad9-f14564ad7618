import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define the AI model types
export type AIModelType = 'openai' | 'anthropic' | 'gemini';

// Define the AI settings interface
interface AISettings {
  enabled: boolean;
  model: AIModelType;
  apiKey: string;
  rateLimited: boolean;
}

// Define the AI context interface
interface AIContextType {
  settings: AISettings;
  updateSettings: (newSettings: Partial<AISettings>) => void;
  resetSettings: () => void;
  enhanceMessage: (message: string) => Promise<string>;
  isEnhancing: boolean;
}

// Default AI settings
const defaultAISettings: AISettings = {
  enabled: true,
  model: 'openai',
  apiKey: '',
  rateLimited: true,
};

// Create the AI context
const AIContext = createContext<AIContextType>({
  settings: defaultAISettings,
  updateSettings: () => {},
  resetSettings: () => {},
  enhanceMessage: async () => '',
  isEnhancing: false,
});

// Hook to use the AI context
export const useAI = () => useContext(AIContext);

// AI provider component
interface AIProviderProps {
  children: ReactNode;
}

export const AIProvider = ({ children }: AIProviderProps) => {
  // Load settings from localStorage or use defaults
  const [settings, setSettings] = useState<AISettings>(() => {
    const savedSettings = localStorage.getItem('ai-settings');
    return savedSettings ? JSON.parse(savedSettings) : defaultAISettings;
  });

  const [isEnhancing, setIsEnhancing] = useState(false);

  // Save settings to localStorage when they change
  useEffect(() => {
    localStorage.setItem('ai-settings', JSON.stringify(settings));
  }, [settings]);

  // Update settings
  const updateSettings = (newSettings: Partial<AISettings>) => {
    setSettings((prev) => ({ ...prev, ...newSettings }));
  };

  // Reset settings to defaults
  const resetSettings = () => {
    setSettings(defaultAISettings);
  };

  // Enhance a message using the selected AI model
  const enhanceMessage = async (message: string): Promise<string> => {
    if (!settings.enabled || !settings.apiKey) {
      return message;
    }

    setIsEnhancing(true);

    try {
      // TODO: Implement actual AI service integration
      // This should call a server-side API to avoid exposing API keys client-side

      // Placeholder implementation
      let enhancedMessage = message;
      
      // TODO: Implement actual AI model integration
      // For now, return the original message unchanged
      enhancedMessage = message;
      
      return enhancedMessage;
    } catch (error) {
      console.error('Error enhancing message:', error);
      return message;
    } finally {
      setIsEnhancing(false);
    }
  };

  return (
    <AIContext.Provider
      value={{
        settings,
        updateSettings,
        resetSettings,
        enhanceMessage,
        isEnhancing,
      }}
    >
      {children}
    </AIContext.Provider>
  );
};
