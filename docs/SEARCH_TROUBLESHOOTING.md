# Search Troubleshooting Guide

## Quick Diagnostics

### 1. Check System Status
```bash
# Verify all containers are running
docker ps

# Expected containers:
# - ats-dashboard-guru-api (port 3001)
# - ats-dashboard-guru-typesense (port 8108)
# - ats-dashboard-guru-postgres
```

### 2. Test Search Health
```bash
curl http://localhost:3001/api/search/health
```

**Expected Response:**
```json
{
  "status": "healthy",
  "responseTime": 37,
  "collections": 3,
  "missingCollections": [],
  "timestamp": "2025-07-03T00:20:55.802Z"
}
```

### 3. Test Basic Search
```bash
curl "http://localhost:3001/api/search?q=test&page=1&per_page=3"
```

## Common Issues and Solutions

### Issue: Search Returns Empty Results

**Symptoms:**
- Search queries return `"found": 0`
- No results in frontend search

**Diagnosis:**
```bash
# Check if collections exist
curl -H "X-TYPESENSE-API-KEY: xyz123" http://localhost:8108/collections

# Check if data exists in collections
curl -H "X-TYPESENSE-API-KEY: xyz123" "http://localhost:8108/collections/jobs/documents/search?q=*&query_by=title"
```

**Solutions:**
1. **Sync data to Typesense:**
   ```bash
   curl -X POST http://localhost:3001/api/search/sync
   ```

2. **Check PostgreSQL data:**
   ```bash
   docker exec -it ats-dashboard-guru-postgres psql -U ats_user -d ats_db -c "SELECT COUNT(*) FROM jobs;"
   ```

3. **Restart Typesense container:**
   ```bash
   docker restart ats-dashboard-guru-typesense
   ```

### Issue: API Connection Refused

**Symptoms:**
- `curl: (7) Failed to connect to localhost port 3001`
- Frontend shows connection errors

**Diagnosis:**
```bash
# Check API container status
docker logs ats-dashboard-guru-api --tail 20

# Check if port is bound
netstat -tlnp | grep 3001
```

**Solutions:**
1. **Restart API container:**
   ```bash
   docker restart ats-dashboard-guru-api
   ```

2. **Check for syntax errors in server.cjs:**
   ```bash
   docker logs ats-dashboard-guru-api --tail 50
   ```

3. **Verify environment variables:**
   ```bash
   docker exec ats-dashboard-guru-api env | grep -E "(NODE_ENV|PORT)"
   ```

### Issue: Typesense Connection Failed

**Symptoms:**
- Health check returns `"status": "unhealthy"`
- Search errors in API logs

**Diagnosis:**
```bash
# Check Typesense container
docker logs ats-dashboard-guru-typesense --tail 20

# Test direct Typesense connection
curl -H "X-TYPESENSE-API-KEY: xyz123" http://localhost:8108/health
```

**Solutions:**
1. **Restart Typesense:**
   ```bash
   docker restart ats-dashboard-guru-typesense
   sleep 10
   curl http://localhost:3001/api/search/health
   ```

2. **Check Typesense data directory:**
   ```bash
   docker exec ats-dashboard-guru-typesense ls -la /data
   ```

3. **Recreate collections:**
   ```bash
   # This will be handled automatically by the sync endpoint
   curl -X POST http://localhost:3001/api/search/sync
   ```

### Issue: Slow Search Performance

**Symptoms:**
- Search takes > 1000ms
- Health check shows high response times

**Diagnosis:**
```bash
# Check system resources
docker stats

# Monitor search response times
curl -w "Time: %{time_total}s\n" "http://localhost:3001/api/search?q=test"
```

**Solutions:**
1. **Optimize query parameters:**
   - Reduce `per_page` and `group_limit`
   - Use specific collection searches when possible

2. **Check system resources:**
   ```bash
   # Free up memory if needed
   docker system prune
   ```

3. **Restart containers:**
   ```bash
   docker restart ats-dashboard-guru-typesense ats-dashboard-guru-api
   ```

### Issue: Data Sync Failures

**Symptoms:**
- New entities don't appear in search
- Sync endpoints return errors

**Diagnosis:**
```bash
# Check API logs for sync errors
docker logs ats-dashboard-guru-api | grep -i "sync"

# Test database connection
docker exec ats-dashboard-guru-api node -e "console.log('DB test')"
```

**Solutions:**
1. **Manual sync:**
   ```bash
   curl -X POST http://localhost:3001/api/search/sync/jobs
   curl -X POST http://localhost:3001/api/search/sync/candidates
   curl -X POST http://localhost:3001/api/search/sync/clients
   ```

2. **Check PostgreSQL connection:**
   ```bash
   docker exec -it ats-dashboard-guru-postgres psql -U ats_user -d ats_db -c "\dt"
   ```

3. **Restart API to reset connections:**
   ```bash
   docker restart ats-dashboard-guru-api
   ```

## Frontend Issues

### Issue: Search Modal Not Opening

**Symptoms:**
- Ctrl+K / Cmd+K doesn't work
- Search button doesn't respond

**Solutions:**
1. **Check browser console for errors**
2. **Verify SearchProvider is wrapping the app**
3. **Check if GlobalSearch component is imported correctly**

### Issue: Search Results Not Displaying

**Symptoms:**
- API returns results but UI shows empty
- Loading state persists

**Solutions:**
1. **Check browser network tab for API calls**
2. **Verify search result mapping in GlobalSearch component**
3. **Check for JavaScript errors in console**

## Performance Monitoring

### Key Metrics to Monitor

1. **Search Response Time:**
   ```bash
   curl -w "Total time: %{time_total}s\n" "http://localhost:3001/api/search?q=test"
   ```

2. **Health Check Response:**
   ```bash
   curl http://localhost:3001/api/search/health | jq '.responseTime'
   ```

3. **Container Resource Usage:**
   ```bash
   docker stats --no-stream
   ```

### Log Analysis

**Search Analytics Logs:**
```bash
# View search analytics in development
docker logs ats-dashboard-guru-api | grep "Search Analytics"
```

**Sync Performance Logs:**
```bash
# View sync performance
docker logs ats-dashboard-guru-api | grep "Sync Analytics"
```

## Emergency Recovery

### Complete System Reset

If all else fails, perform a complete reset:

```bash
# Stop all containers
docker-compose down

# Remove Typesense data (WARNING: This deletes search index)
docker volume rm ats-dashboard-guru_typesense-data

# Restart system
docker-compose up -d

# Wait for containers to start
sleep 30

# Resync all data
curl -X POST http://localhost:3001/api/search/sync

# Verify health
curl http://localhost:3001/api/search/health
```

### Backup and Restore

**Backup Typesense Data:**
```bash
# Create backup
docker exec ats-dashboard-guru-typesense tar -czf /tmp/typesense-backup.tar.gz /data

# Copy backup to host
docker cp ats-dashboard-guru-typesense:/tmp/typesense-backup.tar.gz ./typesense-backup.tar.gz
```

**Restore Typesense Data:**
```bash
# Copy backup to container
docker cp ./typesense-backup.tar.gz ats-dashboard-guru-typesense:/tmp/

# Restore data
docker exec ats-dashboard-guru-typesense tar -xzf /tmp/typesense-backup.tar.gz -C /
```

## Contact and Support

For additional support:
1. Check the main documentation: `docs/SEARCH_DOCUMENTATION.md`
2. Review API logs: `docker logs ats-dashboard-guru-api`
3. Monitor system health: `curl http://localhost:3001/api/search/health`

## Useful Commands Reference

```bash
# Quick health check
curl -s http://localhost:3001/api/search/health | jq '.status'

# Test search functionality
curl -s "http://localhost:3001/api/search?q=test" | jq '.found'

# Sync all data
curl -X POST http://localhost:3001/api/search/sync

# View recent API logs
docker logs ats-dashboard-guru-api --tail 50 --follow

# Check container status
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# System resource usage
docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}"
```
