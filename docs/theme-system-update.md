# Theme System Update

## Overview

The theme system in the ATS Dashboard allows users to select from multiple theme styles (default, cosmic, modern, stargety, tangerine) and modes (light, dark). This document explains the recent updates to the theme system to fix issues with theme selection.

## Problem

The theme system had an issue where selecting a theme required two clicks to apply the theme correctly. This was due to a race condition in how the theme was being updated:

1. When a user clicked on a theme card, both `setThemeStyle` and `setThemeMode` were called separately
2. The second call (`setThemeMode`) was using the old theme style value from state, not the updated one
3. This caused the theme to revert back to the previous style after the first click

## Solution

The solution was to update the theme system to handle both style and mode changes in a single atomic operation:

1. Added a new `setTheme` function to the ThemeContext that updates both style and mode at once
2. Updated the ThemeSelector to use this new function instead of calling `setThemeStyle` and `setThemeMode` separately
3. Improved the `window.updateTheme` function to handle both style and mode updates atomically

## Key Components

### ThemeContext

The ThemeContext provides theme state and functions to the application:

```typescript
interface ThemeContextType {
  themeMode: ThemeMode;
  themeStyle: ThemeStyle;
  setThemeMode: (mode: ThemeMode) => void;
  setThemeStyle: (style: ThemeStyle) => void;
  setTheme: (style: ThemeStyle, mode: ThemeMode) => void;  // New function
  toggleThemeMode: () => void;
}
```

The new `setTheme` function updates both style and mode in a single operation:

```typescript
const handleSetTheme = (style: ThemeStyle, mode: ThemeMode) => {
  // Update state
  setThemeStyle(style);
  setThemeMode(mode);

  // Apply theme in a single operation
  applyTheme(style, mode);
};
```

### ThemeSelector

The ThemeSelector component now uses the `setTheme` function to update the theme:

```typescript
const handleSelectTheme = (themeValue: string, isDark: boolean = false, event?: React.MouseEvent) => {
  try {
    // Prevent event propagation
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Apply directly to DOM for immediate visual feedback
    const html = document.documentElement;
    // ... (DOM updates)

    // Save to localStorage
    localStorage.setItem('themeStyle', themeValue);
    localStorage.setItem('themeMode', isDark ? 'dark' : 'light');

    // Update the context state using the atomic setTheme function
    setTheme(themeValue as ThemeStyle, isDark ? 'dark' : 'light');

    // Close the dialog
    setOpen(false);
  } catch (error) {
    console.error('Error al aplicar el tema:', error);
  }
};
```

### ThemeInitializer

The ThemeInitializer component exposes a global `window.updateTheme` function that updates both style and mode atomically:

```typescript
window.updateTheme = (style?: string, mode?: string) => {
  if (!style && !mode) {
    return;
  }

  // Get current values if not provided
  const newStyle = style || localStorage.getItem('themeStyle') || 'theme-default';
  const newMode = mode || localStorage.getItem('themeMode') || 'light';

  // Apply theme in a single operation
  // ... (DOM updates)

  // Update localStorage
  localStorage.setItem('themeStyle', newStyle);
  localStorage.setItem('themeMode', newMode);
};
```

## Theme Storage

Theme preferences are stored in localStorage with two keys:
- `themeStyle`: Stores the selected theme style (e.g., 'theme-default', 'theme-cosmic')
- `themeMode`: Stores the selected mode ('light' or 'dark')

## CSS Classes

The theme system applies CSS classes to the HTML element:
- Theme style classes: `theme-default`, `theme-cosmic`, `theme-modern`, `theme-stargety`, `theme-tangerine`
- Theme mode class: `dark` (no class for light mode)

## Best Practices

When working with the theme system, follow these best practices:

1. Use the `setTheme` function to update both style and mode at once
2. Avoid calling `setThemeStyle` and `setThemeMode` separately to prevent race conditions
3. Use the `window.updateTheme` function for global theme updates
4. Always update both the DOM and localStorage when changing the theme

## Testing

The theme system can be tested by:
1. Opening the theme selector dialog
2. Clicking on different theme cards
3. Verifying that the theme is applied correctly with a single click
4. Checking that the theme persists after page reload
