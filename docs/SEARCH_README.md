# ATS Dashboard Global Search

A comprehensive search solution for the ATS Dashboard, enabling users to search across jobs, candidates, and clients from a unified interface.

## Features

✅ **Global Search**: Search across all entity types simultaneously  
✅ **Real-time Indexing**: Automatic sync when data is created/updated/deleted  
✅ **Grouped Results**: Results organized by entity type (jobs, candidates, clients)  
✅ **Performance Monitoring**: Health checks and analytics  
✅ **Keyboard Shortcuts**: Quick access with Ctrl+K / Cmd+K  
✅ **Responsive UI**: Works seamlessly on desktop and mobile  
✅ **Error Handling**: Graceful degradation and error recovery  

## Quick Start

### 1. Verify System is Running
```bash
# Check all containers are up
docker ps

# Test search health
curl http://localhost:3001/api/search/health
```

### 2. Sync Initial Data
```bash
# Sync all collections
curl -X POST http://localhost:3001/api/search/sync
```

### 3. Test Search
```bash
# Test API search
curl "http://localhost:3001/api/search?q=engineer&page=1&per_page=3"

# Or use the frontend at http://localhost:5174
# Press Ctrl+K (or Cmd+K on Mac) to open search
```

## Usage

### Frontend Search
1. **Open Search Modal**: Press `Ctrl+K` (Windows/Linux) or `Cmd+K` (Mac)
2. **Type Query**: Enter your search terms
3. **View Results**: Results are grouped by entity type
4. **Navigate**: Click any result to go to the detail page

### API Usage

**Global Search:**
```bash
curl "http://localhost:3001/api/search?q=your-query&page=1&per_page=10"
```

**Collection-Specific Search:**
```bash
curl "http://localhost:3001/api/search/jobs?q=developer&page=1&per_page=5"
curl "http://localhost:3001/api/search/candidates?q=john&page=1&per_page=5"
curl "http://localhost:3001/api/search/clients?q=acme&page=1&per_page=5"
```

**Health Check:**
```bash
curl http://localhost:3001/api/search/health
```

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Typesense     │
│                 │    │                 │    │                 │
│ GlobalSearch    │◄──►│ /api/search     │◄──►│ Collections:    │
│ Component       │    │ /api/search/:id │    │ - jobs          │
│                 │    │ /api/search/sync│    │ - candidates    │
│ SearchProvider  │    │ /api/search/health   │ - clients       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                        ▲                        ▲
         │                        │                        │
         ▼                        ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Search Context  │    │ Real-time Sync  │    │ Search Index    │
│ - State Mgmt    │    │ - Create/Update │    │ - Fast Queries  │
│ - Caching       │    │ - Delete Events │    │ - Faceted Search│
│ - Error Handling│    │ - Auto Indexing │    │ - Relevance     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Data Flow

1. **User Input**: User types in search box
2. **Debouncing**: Frontend waits 300ms before making API call
3. **API Request**: Frontend calls `/api/search` endpoint
4. **Multi-Search**: Backend queries all Typesense collections
5. **Result Processing**: Results are grouped by entity type
6. **Response**: Formatted results returned to frontend
7. **UI Update**: Results displayed in grouped format

## Real-time Sync

The system automatically keeps search indexes updated:

### Automatic Events
- **Job Created**: Immediately indexed in Typesense
- **Candidate Updated**: Re-indexed with new data
- **Client Deleted**: Removed from search index

### Manual Sync
```bash
# Sync specific collection
curl -X POST http://localhost:3001/api/search/sync/jobs

# Sync all collections
curl -X POST http://localhost:3001/api/search/sync
```

## Configuration

### Environment Variables
```env
VITE_TYPESENSE_HOST=typesense
VITE_TYPESENSE_PORT=8108
VITE_TYPESENSE_PROTOCOL=http
VITE_TYPESENSE_API_KEY=xyz123
VITE_API_URL=http://localhost:3001/api
```

### Docker Services
- **API**: Node.js backend on port 3001
- **Typesense**: Search engine on port 8108
- **PostgreSQL**: Primary database
- **Frontend**: React app on port 5174 (dev) / 80 (prod)

## Performance

### Benchmarks
- **Search Response**: < 100ms typical
- **Health Check**: < 50ms typical
- **Sync Operations**: ~1-5ms per document

### Optimization Features
- **Frontend Caching**: 5-minute TTL
- **Debounced Queries**: 300ms delay
- **Grouped Results**: Limits results per entity type
- **Lazy Loading**: Results loaded on demand

## Monitoring

### Health Monitoring
```bash
# Check system health
curl http://localhost:3001/api/search/health

# Expected response:
{
  "status": "healthy",
  "responseTime": 37,
  "collections": 3,
  "missingCollections": [],
  "timestamp": "2025-07-03T00:20:55.802Z"
}
```

### Analytics
- Search queries logged in development mode
- Response times tracked
- Sync performance monitored

## Troubleshooting

### Common Issues

**No Search Results:**
```bash
# Check if data is synced
curl -X POST http://localhost:3001/api/search/sync
```

**API Connection Error:**
```bash
# Restart API container
docker restart ats-dashboard-guru-api
```

**Typesense Connection Failed:**
```bash
# Restart Typesense
docker restart ats-dashboard-guru-typesense
```

### Debug Commands
```bash
# View API logs
docker logs ats-dashboard-guru-api --tail 50

# View Typesense logs
docker logs ats-dashboard-guru-typesense --tail 50

# Check container status
docker ps

# Test direct Typesense connection
curl -H "X-TYPESENSE-API-KEY: xyz123" http://localhost:8108/health
```

## API Reference

### Search Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/search` | GET | Global search across all collections |
| `/api/search/:collection` | GET | Search specific collection |
| `/api/search/health` | GET | System health check |
| `/api/search/sync` | POST | Sync all collections |
| `/api/search/sync/:collection` | POST | Sync specific collection |

### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `q` | string | required | Search query |
| `page` | number | 1 | Page number |
| `per_page` | number | 10 | Results per page |
| `filter_by` | string | '' | Typesense filter |
| `sort_by` | string | '_text_match:desc' | Sort criteria |
| `group_by` | string | 'entity_type' | Group results by field |
| `group_limit` | number | 3 | Max results per group |

## Development

### File Structure
```
src/
├── components/search/
│   └── GlobalSearch.tsx          # Main search component
├── contexts/
│   └── SearchContext.tsx         # Search state management
├── hooks/
│   └── useSearch.ts              # Search hook
├── lib/search/
│   ├── search.ts                 # Search functions
│   ├── collection-schemas.ts     # Typesense schemas
│   ├── search-init.ts           # Initialization
│   └── typesense-client.ts      # Client configuration
└── server/
    └── server.cjs               # Backend API with search endpoints
```

### Adding New Search Fields

1. **Update Collection Schema** (`src/lib/search/collection-schemas.ts`)
2. **Update Sync Logic** (`src/server/server.cjs`)
3. **Update Query Fields** (in search endpoints)
4. **Test and Deploy**

## Documentation

- **[Complete Documentation](./SEARCH_DOCUMENTATION.md)**: Detailed technical documentation
- **[Troubleshooting Guide](./SEARCH_TROUBLESHOOTING.md)**: Common issues and solutions
- **[API Reference](./SEARCH_DOCUMENTATION.md#api-endpoints)**: Complete API documentation

## Support

For issues or questions:
1. Check the troubleshooting guide
2. Review API logs: `docker logs ats-dashboard-guru-api`
3. Test system health: `curl http://localhost:3001/api/search/health`
4. Verify container status: `docker ps`

---

**Status**: ✅ Production Ready  
**Last Updated**: 2025-07-03  
**Version**: 1.0.0
