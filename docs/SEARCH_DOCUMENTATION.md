# ATS Dashboard Global Search Documentation

## Overview

The ATS Dashboard implements a comprehensive global search feature powered by Typesense, enabling users to search across all entities (jobs, candidates, clients) from a unified interface. The search system provides real-time indexing, grouped results, and performance monitoring.

## Architecture

### Components
- **Frontend**: GlobalSearch component with SearchProvider context
- **Backend**: Node.js API with Typesense integration
- **Search Engine**: Typesense running in Docker container
- **Database**: PostgreSQL for primary data storage

### Data Flow
1. User enters search query in GlobalSearch component
2. Frontend calls backend API endpoints
3. Backend queries Typesense collections
4. Results are formatted and returned to frontend
5. Real-time sync keeps Typesense updated with database changes

## API Endpoints

### Search Endpoints

#### Global Search
```
GET /api/search
```

**Parameters:**
- `q` (required): Search query string
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Results per page (default: 10)
- `filter_by` (optional): Typesense filter expression
- `sort_by` (optional): Sort criteria (default: '_text_match:desc')
- `group_by` (optional): Group results by field (default: 'entity_type')
- `group_limit` (optional): Max results per group (default: 3)

**Response:**
```json
{
  "found": 15,
  "page": 1,
  "search_time_ms": 45,
  "request_params": {...},
  "grouped_hits": {
    "jobs": {
      "count": 8,
      "hits": [...]
    },
    "candidates": {
      "count": 5,
      "hits": [...]
    },
    "clients": {
      "count": 2,
      "hits": [...]
    }
  }
}
```

#### Collection-Specific Search
```
GET /api/search/:collection
```

**Parameters:**
- `collection` (required): Collection name (jobs, candidates, clients)
- Same query parameters as global search

**Response:**
```json
{
  "found": 8,
  "page": 1,
  "search_time_ms": 23,
  "request_params": {...},
  "hits": [...]
}
```

### Data Synchronization Endpoints

#### Sync Single Collection
```
POST /api/search/sync/:collection
```

Synchronizes data from PostgreSQL to Typesense for a specific collection.

**Response:**
```json
{
  "success": true,
  "collection": "jobs",
  "syncCount": 25,
  "syncTime": 150,
  "message": "Successfully synced 25 jobs to search index"
}
```

#### Sync All Collections
```
POST /api/search/sync
```

Synchronizes all collections (jobs, candidates, clients).

**Response:**
```json
{
  "success": true,
  "message": "Sync completed for all collections",
  "results": {
    "jobs": {...},
    "candidates": {...},
    "clients": {...}
  }
}
```

### Monitoring Endpoints

#### Health Check
```
GET /api/search/health
```

**Response:**
```json
{
  "status": "healthy",
  "responseTime": 37,
  "collections": 3,
  "missingCollections": [],
  "timestamp": "2025-07-03T00:20:55.802Z"
}
```

## Collection Schemas

### Jobs Collection
```javascript
{
  id: string,
  title: string,
  description: string,
  entity_type: 'jobs',
  created_at: number,
  updated_at: number,
  company: string,
  location: string,
  salary_range: string,
  job_type: string,
  status: string,
  skills: string[]
}
```

### Candidates Collection
```javascript
{
  id: string,
  title: string,
  description: string,
  entity_type: 'candidates',
  created_at: number,
  updated_at: number,
  name: string,
  email: string,
  phone: string,
  location: string,
  skills: string[],
  experience_years: number,
  status: string,
  secondary_status: string
}
```

### Clients Collection
```javascript
{
  id: string,
  title: string,
  description: string,
  entity_type: 'clients',
  created_at: number,
  updated_at: number,
  company_name: string,
  contact_name: string,
  email: string,
  phone: string,
  industry: string,
  location: string
}
```

## Real-Time Data Synchronization

The system automatically updates Typesense indexes when entities are modified through the API:

### Automatic Sync Events
- **Create**: New entities are indexed immediately after creation
- **Update**: Modified entities are re-indexed with updated data
- **Delete**: Entities are removed from search index

### Sync Implementation
- Sync operations are non-blocking and don't fail API requests
- Errors are logged but don't affect primary operations
- Manual sync endpoints available for data recovery

## Frontend Integration

### GlobalSearch Component
Located at `src/components/search/GlobalSearch.tsx`

**Features:**
- Real-time search with debouncing
- Grouped result display
- Navigation to entity detail pages
- Keyboard shortcuts (Ctrl+K / Cmd+K)
- Loading states and error handling

### Search Context
Located at `src/contexts/SearchContext.tsx`

**Provides:**
- Search state management
- API integration
- Caching for performance
- Error handling

### Search Functions
Located at `src/lib/search/search.ts`

**Key Functions:**
- `searchAll()`: Multi-collection search
- `searchCollection()`: Single collection search
- Caching and error handling

## Performance Optimization

### Caching
- Frontend implements in-memory caching with TTL
- Cache keys based on query parameters
- Automatic cache invalidation

### Debouncing
- Search queries are debounced to reduce API calls
- Configurable delay (default: 300ms)

### Analytics
- Search queries are logged for performance monitoring
- Response times tracked
- Usage analytics for optimization

## Troubleshooting

### Common Issues

#### Search Returns No Results
1. Check Typesense container status: `docker ps`
2. Verify collections exist: `curl http://localhost:8108/collections`
3. Check data sync: `POST /api/search/sync`
4. Review API logs: `docker logs ats-dashboard-guru-api`

#### Slow Search Performance
1. Check health endpoint: `GET /api/search/health`
2. Monitor response times in logs
3. Consider index optimization
4. Review query complexity

#### Sync Failures
1. Check PostgreSQL connection
2. Verify Typesense connectivity
3. Review sync endpoint logs
4. Manual sync via API endpoints

### Monitoring Commands

```bash
# Check all containers
docker ps

# Check API logs
docker logs ats-dashboard-guru-api --tail 50

# Check Typesense logs
docker logs ats-dashboard-guru-typesense --tail 50

# Test search health
curl http://localhost:3001/api/search/health

# Manual sync
curl -X POST http://localhost:3001/api/search/sync

# Test search functionality
curl "http://localhost:3001/api/search?q=test&page=1&per_page=5"
```

## Configuration

### Environment Variables
- `VITE_TYPESENSE_HOST`: Typesense server host (default: 'typesense')
- `VITE_TYPESENSE_PORT`: Typesense server port (default: '8108')
- `VITE_TYPESENSE_PROTOCOL`: Protocol (default: 'http')
- `VITE_TYPESENSE_API_KEY`: API key (default: 'xyz123')
- `VITE_API_URL`: Backend API URL (default: 'http://localhost:3001/api')

### Docker Configuration
Typesense runs in Docker with persistent data storage and proper networking configuration.

## Future Enhancements

### Potential Improvements
- Advanced filtering and faceted search
- Search result highlighting
- Auto-complete suggestions
- Search analytics dashboard
- Elasticsearch migration option
- Full-text search optimization
- Custom ranking algorithms

### Scalability Considerations
- Typesense cluster setup for high availability
- Search result pagination optimization
- Index partitioning strategies
- Caching layer improvements
