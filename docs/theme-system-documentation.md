# Sistema de Temas - Documentación

## Visión General

El sistema de temas en ATS Dashboard Guru permite a los usuarios personalizar la apariencia de la aplicación mediante la selección de diferentes temas y modos (claro/oscuro). Este documento explica cómo está implementado el sistema y cómo funciona.

## Conceptos Clave

### <PERSON><PERSON> vs Mo<PERSON>

- **Temas**: Definen el conjunto de colores y estilos (ej. "theme-default", "theme-tangerine", etc.)
- **Modos**: Definen si se usa la variante clara u oscura de un tema (solo hay dos modos: claro y oscuro)

### Implementación

El sistema utiliza:
- **next-themes**: Biblioteca para gestionar temas en aplicaciones React
- **UserContext**: Contexto personalizado para almacenar las preferencias del usuario
- **CSS Variables**: Variables CSS para definir los colores y estilos de cada tema

## Estructura de Archivos

- `src/themes/*.css`: Archivos CSS que definen las variables para cada tema
- `src/components/layout/ThemeToggle.tsx`: Componente para alternar entre modos claro/oscuro y seleccionar temas
- `src/components/layout/ThemeSelector.tsx`: Componente para seleccionar temas con una interfaz visual
- `src/contexts/UserContext.tsx`: Contexto para almacenar y sincronizar las preferencias del usuario

## Cómo Funciona

### Selección de Tema

1. El usuario selecciona un tema (ej. "theme-default", "theme-tangerine", etc.)
2. La clase correspondiente se aplica al elemento HTML raíz
3. Las variables CSS del tema seleccionado se aplican a toda la aplicación
4. La preferencia se guarda en el contexto del usuario y en localStorage

### Selección de Modo (Claro/Oscuro)

1. El usuario alterna entre modo claro y oscuro
2. Si se selecciona el modo oscuro, se añade la clase "dark" al elemento HTML raíz
3. Si se selecciona el modo claro, se elimina la clase "dark" del elemento HTML raíz
4. La preferencia se guarda en el contexto del usuario y en localStorage

### Clases CSS

- **Temas**: `theme-default`, `theme-tangerine`, `theme-stargety`, `theme-modern`, `theme-cosmic`
- **Modos**: Solo se usa la clase `dark` para el modo oscuro (el modo claro es la ausencia de esta clase)

## Añadir un Nuevo Tema

Para añadir un nuevo tema:

1. Crear un nuevo archivo CSS en `src/themes/` (ej. `nuevo-tema.css`)
2. Definir las variables CSS para el tema (tanto para modo claro como oscuro)
3. Importar el archivo CSS en `src/index.css`
4. Añadir el tema a la lista de temas en `src/main.tsx`
5. Añadir el tema a las opciones en `src/components/layout/ThemeSelector.tsx`

## Mejores Prácticas

1. **No usar la clase "light"**: La clase "light" no debe usarse, ya que el modo claro es la ausencia de la clase "dark"
2. **Limpiar clases antes de aplicar nuevas**: Siempre limpiar las clases existentes antes de aplicar nuevas para evitar conflictos
3. **Sincronizar con UserContext**: Asegurarse de que las preferencias del usuario se actualicen correctamente
4. **Usar variables CSS**: Usar las variables CSS definidas en los archivos de tema en lugar de colores hardcodeados

## Solución de Problemas

Si hay problemas con el sistema de temas:

1. Verificar que las clases se estén aplicando correctamente al elemento HTML raíz
2. Verificar que las preferencias del usuario se estén guardando correctamente
3. Verificar que los archivos CSS de los temas estén siendo importados correctamente
4. Verificar que las variables CSS estén definidas correctamente para cada tema
