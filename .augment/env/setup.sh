#!/bin/bash

# Update system packages
sudo apt-get update

# Install Node.js 20 (LTS)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js and npm installation
node --version
npm --version

# Install dependencies
npm install

# Install Playwright browsers
npx playwright install

# Install system dependencies for Playwright browsers
npx playwright install-deps

# Build the application
npm run build

# Add npm global bin to PATH
echo 'export PATH="$HOME/.npm-global/bin:$PATH"' >> $HOME/.profile
mkdir -p $HOME/.npm-global
npm config set prefix $HOME/.npm-global

# Source the profile to update PATH
source $HOME/.profile