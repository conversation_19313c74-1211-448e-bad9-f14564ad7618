#!/bin/bash

# Unified ATS Dashboard Guru Deployment Script
# Combines functionality from start-docker-desktop.sh, stop-docker.sh, and deploy.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start     Start the application with Docker Desktop (default)"
    echo "  stop      Stop all containers"
    echo "  restart   Stop and start the application"
    echo "  deploy    Deploy for production"
    echo "  status    Show container status"
    echo "  logs      Show container logs"
    echo "  clean     Stop containers and remove volumes"
    echo ""
    echo "Options:"
    echo "  --dev     Use development configuration (with docker-compose.dev.yml)"
    echo "  --prod    Use production configuration (default)"
    echo "  --help    Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start --dev    # Start with development configuration"
    echo "  $0 deploy         # Deploy for production"
    echo "  $0 stop           # Stop all containers"
    echo "  $0 logs           # Show logs"
}

# Function to configure Docker Desktop context
configure_docker_context() {
    print_status "🐳 Configuring Docker Desktop context..."
    
    if [ -f "./scripts/setup-docker-desktop.sh" ]; then
        ./scripts/setup-docker-desktop.sh
    else
        # Fallback configuration
        if docker context ls | grep -q "desktop-linux"; then
            docker context use desktop-linux
            unset DOCKER_HOST
            print_success "✅ Using Docker Desktop context"
        else
            print_warning "⚠️  Docker Desktop context not found, using default"
        fi
    fi
}

# Function to create necessary directories
create_directories() {
    print_status "📁 Creating necessary directories..."
    mkdir -p ./data/minio
}

# Function to build the application
build_application() {
    print_status "📦 Building application..."
    npm run build
}

# Function to start containers
start_containers() {
    local use_dev_config=$1
    
    print_status "🐳 Starting Docker containers..."
    
    if [ "$use_dev_config" = true ]; then
        print_status "📋 Using development configuration"
        docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d
    else
        print_status "📋 Using production configuration"
        docker compose up -d
    fi
}

# Function to wait for services
wait_for_services() {
    print_status "⏳ Waiting for services to initialize..."
    sleep 30
}

# Function to initialize MinIO (for production)
initialize_minio() {
    print_status "📦 Initializing MinIO buckets..."
    docker compose exec minio mc mb /data/uploads 2>/dev/null || true
    docker compose exec minio mc policy set public /data/uploads 2>/dev/null || true
}

# Function to show container status
show_status() {
    print_status "📊 Container status:"
    docker compose ps
}

# Function to show access points
show_access_points() {
    echo ""
    print_success "🎉 Application started successfully!"
    echo ""
    print_status "📱 Access your application:"
    echo "   🌐 Frontend: http://localhost"
    echo "   🔧 API: http://localhost:3001"
    echo "   🗄️  pgAdmin: http://localhost:5050"
    echo "   📦 MinIO Console: http://localhost:9001"
    echo "   🔍 Typesense: http://localhost:8108"
    echo ""
    print_status "🐳 Your containers are now visible in Docker Desktop!"
    print_status "💡 To stop: $0 stop"
}

# Function to stop containers
stop_containers() {
    print_status "🛑 Stopping ATS Dashboard Guru containers..."
    configure_docker_context
    docker compose down
    print_success "✅ All containers stopped and removed"
    print_status "💾 Data volumes are preserved"
    echo ""
    print_status "💡 To start again: $0 start"
    print_status "🗑️  To remove all data: $0 clean"
}

# Function to show logs
show_logs() {
    print_status "📋 Showing container logs..."
    docker compose logs -f
}

# Function to clean everything
clean_all() {
    print_status "🗑️  Stopping containers and removing volumes..."
    configure_docker_context
    docker compose down -v
    print_success "✅ All containers and volumes removed"
}

# Main execution logic
main() {
    local command="start"
    local use_dev_config=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            start|stop|restart|deploy|status|logs|clean)
                command=$1
                shift
                ;;
            --dev)
                use_dev_config=true
                shift
                ;;
            --prod)
                use_dev_config=false
                shift
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Execute command
    case $command in
        start)
            print_status "🚀 Starting ATS Dashboard Guru..."
            configure_docker_context
            create_directories
            build_application
            start_containers $use_dev_config
            wait_for_services
            show_status
            show_access_points
            ;;
        stop)
            stop_containers
            ;;
        restart)
            print_status "🔄 Restarting ATS Dashboard Guru..."
            stop_containers
            echo ""
            main start $([ "$use_dev_config" = true ] && echo "--dev" || echo "--prod")
            ;;
        deploy)
            print_status "🚀 Deploying ATS Dashboard Guru for production..."
            configure_docker_context
            create_directories
            build_application
            start_containers false
            wait_for_services
            initialize_minio
            show_status
            show_access_points
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        clean)
            clean_all
            ;;
        *)
            print_error "Unknown command: $command"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
